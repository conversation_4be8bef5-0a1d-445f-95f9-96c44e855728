import postgres from "postgres";
import * as dotenv from "dotenv";

// Load environment variables
dotenv.config({ path: ".env.local" });

const sql = postgres(process.env.DATABASE_URL || "");

async function resetDatabase() {
  console.log("🗑️  Dropping all tables...");

  // Drop all tables in the correct order (reverse of dependencies)
  const dropQueries = [
    "DROP TABLE IF EXISTS user_video_progress CASCADE;",
    "DROP TABLE IF EXISTS user_topic_progress CASCADE;",
    "DROP TABLE IF EXISTS user_quiz_attempts CASCADE;",
    "DROP TABLE IF EXISTS user_notes CASCADE;",
    "DROP TABLE IF EXISTS user_pets CASCADE;",
    "DROP TABLE IF EXISTS user_xp CASCADE;",
    "DROP TABLE IF EXISTS user_streaks CASCADE;",
    "DROP TABLE IF EXISTS user_badges CASCADE;",
    "DROP TABLE IF EXISTS user_role_assignments CASCADE;",
    "DROP TABLE IF EXISTS user_relationships CASCADE;",
    "DROP TABLE IF EXISTS user_profiles CASCADE;",
    "DROP TABLE IF EXISTS sessions CASCADE;",
    "DROP TABLE IF EXISTS accounts CASCADE;",
    "DROP TABLE IF EXISTS verifications CASCADE;",
    "DROP TABLE IF EXISTS videos CASCADE;",
    "DROP TABLE IF EXISTS quiz_questions CASCADE;",
    "DROP TABLE IF EXISTS quizzes CASCADE;",
    "DROP TABLE IF EXISTS topics CASCADE;",
    "DROP TABLE IF EXISTS subjects CASCADE;",
    "DROP TABLE IF EXISTS pets CASCADE;",
    "DROP TABLE IF EXISTS badges CASCADE;",
    "DROP TABLE IF EXISTS user_roles CASCADE;",
    "DROP TABLE IF EXISTS users CASCADE;",

    // Drop enums
    "DROP TYPE IF EXISTS content_status CASCADE;",
    "DROP TYPE IF EXISTS difficulty_level CASCADE;",
    "DROP TYPE IF EXISTS question_type CASCADE;",
    "DROP TYPE IF EXISTS badge_type CASCADE;",
    "DROP TYPE IF EXISTS streak_type CASCADE;",
    "DROP TYPE IF EXISTS personality_trait CASCADE;",
    "DROP TYPE IF EXISTS pet_type CASCADE;",
    "DROP TYPE IF EXISTS note_type CASCADE;",
    "DROP TYPE IF EXISTS progress_status CASCADE;",
    "DROP TYPE IF EXISTS relationship_type CASCADE;",
    "DROP TYPE IF EXISTS role_type CASCADE;",
    "DROP TYPE IF EXISTS educational_board CASCADE;",
    "DROP TYPE IF EXISTS standard CASCADE;",
  ];

  for (const query of dropQueries) {
    try {
      await sql.unsafe(query);
      console.log(`✅ ${query}`);
    } catch (error) {
      console.log(`⚠️  ${query} - ${error}`);
    }
  }

  console.log("✅ Database reset complete!");
  await sql.end();
}

resetDatabase().catch(console.error);
