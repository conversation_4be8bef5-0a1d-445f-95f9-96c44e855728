#!/usr/bin/env tsx

// Check for required environment variables before importing anything
const DATABASE_URL = process.env.DATABASE_URL;
if (!DATABASE_URL) {
  console.error("❌ DATABASE_URL environment variable is required");
  console.log(
    "💡 Create a .env.local file with DATABASE_URL=your_database_url"
  );
  process.exit(1);
}

import { seedDatabase } from "../src/lib/db/seeds";

/**
 * Runs the database seeding process and exits the process based on the outcome.
 *
 * Invokes the `seedDatabase` function and logs the result. Exits with a success code if seeding completes, or a failure code if an error occurs.
 */
async function main() {
  try {
    await seedDatabase();
    console.log("✅ Database seeding completed successfully!");
    process.exit(0);
  } catch (error) {
    console.error("❌ Database seeding failed:", error);
    process.exit(1);
  }
}

main();
