"use client";

import { <PERSON><PERSON><PERSON>cle, Loader2, Mail, RefreshCw, XCircle } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { authClient } from "@/lib/auth-client";

type VerificationStatus =
	| "pending"
	| "verifying"
	| "success"
	| "error"
	| "resending";

export function EmailVerification() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [status, setStatus] = useState<VerificationStatus>("pending");
	const [error, setError] = useState("");
	const [resendCooldown, setResendCooldown] = useState(0);

	const email = searchParams.get("email") || "";
	const token = searchParams.get("token");

	const verifyEmail = useCallback(
		async (verificationToken: string) => {
			setStatus("verifying");
			setError("");

			try {
				const result = await authClient.verifyEmail({
					query: {
						token: verificationToken,
					},
				});

				if (result.error) {
					setStatus("error");
					setError(
						result.error.message ||
							"Email verification failed. The link may be expired or invalid.",
					);
				} else {
					setStatus("success");
					// Redirect to dashboard after successful verification
					setTimeout(() => {
						router.push("/dashboard");
					}, 2000);
				}
			} catch (error) {
				console.error("Email verification error:", error);
				setStatus("error");
				setError("An unexpected error occurred during verification.");
			}
		},
		[router],
	);

	// Auto-verify if token is present in URL
	useEffect(() => {
		if (token) {
			verifyEmail(token);
		}
	}, [token, verifyEmail]);

	// Resend cooldown timer
	useEffect(() => {
		if (resendCooldown > 0) {
			const timer = setTimeout(() => {
				setResendCooldown(resendCooldown - 1);
			}, 1000);
			return () => clearTimeout(timer);
		}
	}, [resendCooldown]);

	const resendVerificationEmail = async () => {
		if (!email) {
			setError("Email address is required to resend verification.");
			return;
		}

		setStatus("resending");
		setError("");

		try {
			const result = await authClient.sendVerificationEmail({
				email,
				callbackURL: `${window.location.origin}/auth/verify-email`,
			});

			if (result.error) {
				setError(
					result.error.message || "Failed to resend verification email.",
				);
				setStatus("pending");
			} else {
				setStatus("pending");
				setResendCooldown(60); // 60 second cooldown
			}
		} catch (error) {
			console.error("Resend verification error:", error);
			setError("An unexpected error occurred while resending the email.");
			setStatus("pending");
		}
	};

	const renderContent = () => {
		switch (status) {
			case "verifying":
				return (
					<>
						<div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
							<Loader2 className="h-6 w-6 text-blue-600 animate-spin" />
						</div>
						<CardTitle className="text-2xl font-bold text-center">
							Verifying Email
						</CardTitle>
						<CardDescription className="text-center">
							Please wait while we verify your email address...
						</CardDescription>
					</>
				);

			case "success":
				return (
					<>
						<div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
							<CheckCircle className="h-6 w-6 text-green-600" />
						</div>
						<CardTitle className="text-2xl font-bold text-center">
							Email Verified!
						</CardTitle>
						<CardDescription className="text-center">
							Your email has been successfully verified. Redirecting to
							dashboard...
						</CardDescription>
					</>
				);

			case "error":
				return (
					<>
						<div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
							<XCircle className="h-6 w-6 text-red-600" />
						</div>
						<CardTitle className="text-2xl font-bold text-center">
							Verification Failed
						</CardTitle>
						<CardDescription className="text-center">
							We couldn't verify your email address. The link may be expired or
							invalid.
						</CardDescription>
					</>
				);

			default:
				return (
					<>
						<div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
							<Mail className="h-6 w-6 text-blue-600" />
						</div>
						<CardTitle className="text-2xl font-bold text-center">
							Check Your Email
						</CardTitle>
						<CardDescription className="text-center">
							{email ? (
								<>
									We've sent a verification link to <strong>{email}</strong>
								</>
							) : (
								"We've sent you a verification email"
							)}
						</CardDescription>
					</>
				);
		}
	};

	const renderActions = () => {
		if (status === "success") {
			return (
				<Button className="w-full" onClick={() => router.push("/dashboard")}>
					Continue to Dashboard
				</Button>
			);
		}

		if (status === "error" || status === "pending" || status === "resending") {
			return (
				<div className="space-y-2">
					{email && (
						<Button
							variant="outline"
							className="w-full"
							onClick={resendVerificationEmail}
							disabled={status === "resending" || resendCooldown > 0}
						>
							{status === "resending" ? (
								<>
									<Loader2 className="mr-2 h-4 w-4 animate-spin" />
									Sending...
								</>
							) : resendCooldown > 0 ? (
								<>
									<RefreshCw className="mr-2 h-4 w-4" />
									Resend in {resendCooldown}s
								</>
							) : (
								<>
									<RefreshCw className="mr-2 h-4 w-4" />
									Resend Verification Email
								</>
							)}
						</Button>
					)}

					<Button
						variant="ghost"
						className="w-full"
						onClick={() => router.push("/auth/login")}
					>
						Back to Sign In
					</Button>
				</div>
			);
		}

		return null;
	};

	return (
		<Card className="w-full max-w-md mx-auto">
			<CardHeader className="space-y-1 text-center">
				{renderContent()}
			</CardHeader>
			<CardContent className="space-y-4">
				{error && (
					<Alert variant="destructive">
						<AlertDescription>{error}</AlertDescription>
					</Alert>
				)}

				{status === "pending" && (
					<div className="text-sm text-muted-foreground text-center">
						<p>
							Click the verification link in the email to activate your account.
						</p>
						<p className="mt-2">
							If you don't see the email, check your spam folder.
						</p>
					</div>
				)}

				{renderActions()}
			</CardContent>
		</Card>
	);
}
