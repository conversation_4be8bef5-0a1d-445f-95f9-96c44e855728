"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ff, <PERSON>ader2 } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { authClient } from "@/lib/auth-client";

interface ResetPasswordFormData {
	password: string;
	confirmPassword: string;
}

export function ResetPasswordForm() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [formData, setFormData] = useState<ResetPasswordFormData>({
		password: "",
		confirmPassword: "",
	});
	const [errors, setErrors] = useState<Partial<ResetPasswordFormData>>({});
	const [isLoading, setIsLoading] = useState(false);
	const [showPassword, setShowPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);
	const [submitError, setSubmitError] = useState("");
	const [isSuccess, setIsSuccess] = useState(false);

	const token = searchParams.get("token");

	useEffect(() => {
		if (!token) {
			setSubmitError(
				"Invalid or missing reset token. Please request a new password reset link.",
			);
		}
	}, [token]);

	const validateForm = (): boolean => {
		const newErrors: Partial<ResetPasswordFormData> = {};

		// Password validation
		if (!formData.password) {
			newErrors.password = "Password is required";
		} else if (formData.password.length < 8) {
			newErrors.password = "Password must be at least 8 characters";
		} else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
			newErrors.password =
				"Password must contain at least one uppercase letter, one lowercase letter, and one number";
		}

		// Confirm password validation
		if (!formData.confirmPassword) {
			newErrors.confirmPassword = "Please confirm your password";
		} else if (formData.password !== formData.confirmPassword) {
			newErrors.confirmPassword = "Passwords do not match";
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	const handleInputChange = (
		field: keyof ResetPasswordFormData,
		value: string,
	) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
		// Clear error for this field when user starts typing
		if (errors[field]) {
			setErrors((prev) => ({ ...prev, [field]: undefined }));
		}
		// Clear submit error when user makes changes
		if (submitError) {
			setSubmitError("");
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!token) {
			setSubmitError(
				"Invalid reset token. Please request a new password reset link.",
			);
			return;
		}

		if (!validateForm()) {
			return;
		}

		setIsLoading(true);
		setSubmitError("");

		try {
			const result = await authClient.resetPassword({
				token,
				newPassword: formData.password,
			});

			if (result.error) {
				if (result.error.code === "INVALID_TOKEN") {
					setSubmitError(
						"This reset link has expired or is invalid. Please request a new one.",
					);
				} else {
					setSubmitError(
						result.error.message ||
							"Failed to reset password. Please try again.",
					);
				}
			} else {
				setIsSuccess(true);
				// Redirect to login after successful reset
				setTimeout(() => {
					router.push("/auth/login?message=password-reset-success");
				}, 3000);
			}
		} catch (error) {
			console.error("Password reset error:", error);
			setSubmitError("An unexpected error occurred. Please try again.");
		} finally {
			setIsLoading(false);
		}
	};

	if (isSuccess) {
		return (
			<Card className="w-full max-w-md mx-auto">
				<CardHeader className="space-y-1 text-center">
					<div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
						<CheckCircle className="h-6 w-6 text-green-600" />
					</div>
					<CardTitle className="text-2xl font-bold">
						Password Reset Successful
					</CardTitle>
					<CardDescription>
						Your password has been successfully reset. You can now sign in with
						your new password.
					</CardDescription>
				</CardHeader>
				<CardContent>
					<Button className="w-full" onClick={() => router.push("/auth/login")}>
						Continue to Sign In
					</Button>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="w-full max-w-md mx-auto">
			<CardHeader className="space-y-1">
				<CardTitle className="text-2xl font-bold text-center">
					Reset Your Password
				</CardTitle>
				<CardDescription className="text-center">
					Enter your new password below
				</CardDescription>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					{submitError && (
						<Alert variant="destructive">
							<AlertDescription>{submitError}</AlertDescription>
						</Alert>
					)}

					<div className="space-y-2">
						<Label htmlFor="password">New Password</Label>
						<div className="relative">
							<Input
								id="password"
								type={showPassword ? "text" : "password"}
								placeholder="Enter your new password"
								value={formData.password}
								onChange={(e) => handleInputChange("password", e.target.value)}
								disabled={isLoading || !token}
								className={errors.password ? "border-red-500 pr-10" : "pr-10"}
								autoComplete="new-password"
							/>
							<Button
								type="button"
								variant="ghost"
								size="sm"
								className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
								onClick={() => setShowPassword(!showPassword)}
								disabled={isLoading || !token}
							>
								{showPassword ? (
									<EyeOff className="h-4 w-4" />
								) : (
									<Eye className="h-4 w-4" />
								)}
							</Button>
						</div>
						{errors.password && (
							<p className="text-sm text-red-500">{errors.password}</p>
						)}
					</div>

					<div className="space-y-2">
						<Label htmlFor="confirmPassword">Confirm New Password</Label>
						<div className="relative">
							<Input
								id="confirmPassword"
								type={showConfirmPassword ? "text" : "password"}
								placeholder="Confirm your new password"
								value={formData.confirmPassword}
								onChange={(e) =>
									handleInputChange("confirmPassword", e.target.value)
								}
								disabled={isLoading || !token}
								className={
									errors.confirmPassword ? "border-red-500 pr-10" : "pr-10"
								}
								autoComplete="new-password"
							/>
							<Button
								type="button"
								variant="ghost"
								size="sm"
								className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
								onClick={() => setShowConfirmPassword(!showConfirmPassword)}
								disabled={isLoading || !token}
							>
								{showConfirmPassword ? (
									<EyeOff className="h-4 w-4" />
								) : (
									<Eye className="h-4 w-4" />
								)}
							</Button>
						</div>
						{errors.confirmPassword && (
							<p className="text-sm text-red-500">{errors.confirmPassword}</p>
						)}
					</div>

					<Button
						type="submit"
						className="w-full"
						disabled={isLoading || !token}
					>
						{isLoading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Resetting Password...
							</>
						) : (
							"Reset Password"
						)}
					</Button>
				</form>

				<div className="mt-4 text-center">
					<Button
						variant="link"
						className="text-sm"
						onClick={() => router.push("/auth/login")}
						disabled={isLoading}
					>
						Back to Sign In
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}
