"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { authClient } from "@/lib/auth-client";

export function ForgotPasswordForm() {
	const router = useRouter();
	const [email, setEmail] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	const [isSuccess, setIsSuccess] = useState(false);

	const validateEmail = (email: string): boolean => {
		return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!email.trim()) {
			setError("Email is required");
			return;
		}

		if (!validateEmail(email)) {
			setError("Please enter a valid email address");
			return;
		}

		setIsLoading(true);
		setError("");

		try {
			const result = await authClient.forgetPassword({
				email: email.trim(),
				redirectTo: `${window.location.origin}/auth/reset-password`,
			});

			if (result.error) {
				setError(
					result.error.message ||
						"Failed to send reset email. Please try again.",
				);
			} else {
				setIsSuccess(true);
			}
		} catch (error) {
			console.error("Forgot password error:", error);
			setError("An unexpected error occurred. Please try again.");
		} finally {
			setIsLoading(false);
		}
	};

	const handleInputChange = (value: string) => {
		setEmail(value);
		if (error) {
			setError("");
		}
	};

	if (isSuccess) {
		return (
			<Card className="w-full max-w-md mx-auto">
				<CardHeader className="space-y-1 text-center">
					<div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
						<CheckCircle className="h-6 w-6 text-green-600" />
					</div>
					<CardTitle className="text-2xl font-bold">Check Your Email</CardTitle>
					<CardDescription>
						We've sent a password reset link to <strong>{email}</strong>
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="text-sm text-muted-foreground text-center">
						<p>Click the link in the email to reset your password.</p>
						<p className="mt-2">
							If you don't see the email, check your spam folder.
						</p>
					</div>

					<div className="space-y-2">
						<Button
							variant="outline"
							className="w-full"
							onClick={() => {
								setIsSuccess(false);
								setEmail("");
							}}
						>
							Send Another Email
						</Button>

						<Button
							variant="ghost"
							className="w-full"
							onClick={() => router.push("/auth/login")}
						>
							<ArrowLeft className="mr-2 h-4 w-4" />
							Back to Sign In
						</Button>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="w-full max-w-md mx-auto">
			<CardHeader className="space-y-1">
				<CardTitle className="text-2xl font-bold text-center">
					Reset Password
				</CardTitle>
				<CardDescription className="text-center">
					Enter your email address and we'll send you a link to reset your
					password
				</CardDescription>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					{error && (
						<Alert variant="destructive">
							<AlertDescription>{error}</AlertDescription>
						</Alert>
					)}

					<div className="space-y-2">
						<Label htmlFor="email">Email Address</Label>
						<Input
							id="email"
							type="email"
							placeholder="Enter your email address"
							value={email}
							onChange={(e) => handleInputChange(e.target.value)}
							disabled={isLoading}
							className={error ? "border-red-500" : ""}
							autoComplete="email"
							autoFocus
						/>
					</div>

					<Button type="submit" className="w-full" disabled={isLoading}>
						{isLoading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Sending Reset Link...
							</>
						) : (
							"Send Reset Link"
						)}
					</Button>
				</form>

				<div className="mt-4">
					<Button
						variant="ghost"
						className="w-full"
						onClick={() => router.push("/auth/login")}
						disabled={isLoading}
					>
						<ArrowLeft className="mr-2 h-4 w-4" />
						Back to Sign In
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}
