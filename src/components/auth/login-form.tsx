"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { <PERSON>, <PERSON>Off, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { api } from "@/trpc/client";

// Validation schema
const loginSchema = z.object({
	email: z.string().email("Please enter a valid email address"),
	password: z.string().min(1, "Password is required"),
});

type LoginFormData = z.infer<typeof loginSchema>;

export function LoginForm() {
	const router = useRouter();
	const [showPassword, setShowPassword] = useState(false);

	const form = useForm<LoginFormData>({
		resolver: zodResolver(loginSchema),
	});

	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting },
		setError,
	} = form;

	const signInMutation = api.auth.signIn.useMutation({
		onSuccess: (data) => {
			toast.success(data.message);
			const redirectTo =
				new URLSearchParams(window.location.search).get("redirect") ||
				"/dashboard";
			router.push(redirectTo);
		},
		onError: (error) => {
			if (error.data?.code === "FORBIDDEN") {
				toast.error("Please verify your email address before signing in");
				setTimeout(() => {
					router.push(
						`/auth/verify-email?email=${encodeURIComponent(form.getValues("email"))}`,
					);
				}, 2000);
			} else if (error.data?.code === "UNAUTHORIZED") {
				setError("email", { message: "Invalid email or password" });
				setError("password", { message: "Invalid email or password" });
			} else {
				toast.error(error.message);
			}
		},
	});

	const onSubmit = (data: LoginFormData) => {
		signInMutation.mutate(data);
	};

	return (
		<Card className="w-full max-w-md mx-auto">
			<CardHeader className="space-y-1">
				<CardTitle className="text-2xl font-bold text-center">
					Welcome Back
				</CardTitle>
				<CardDescription className="text-center">
					Sign in to your LearnFunda account
				</CardDescription>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
					{signInMutation.error && (
						<Alert variant="destructive">
							<AlertDescription>
								{signInMutation.error.message}
							</AlertDescription>
						</Alert>
					)}

					<div className="space-y-2">
						<Label htmlFor="email">Email</Label>
						<Input
							id="email"
							type="email"
							placeholder="Enter your email"
							{...register("email")}
							disabled={isSubmitting}
							className={errors.email ? "border-red-500" : ""}
							autoComplete="email"
						/>
						{errors.email && (
							<p className="text-sm text-red-500">{errors.email.message}</p>
						)}
					</div>

					<div className="space-y-2">
						<div className="flex items-center justify-between">
							<Label htmlFor="password">Password</Label>
							<Button
								variant="link"
								className="p-0 h-auto text-sm font-normal"
								onClick={() => router.push("/auth/forgot-password")}
								disabled={isSubmitting}
								type="button"
							>
								Forgot password?
							</Button>
						</div>
						<div className="relative">
							<Input
								id="password"
								type={showPassword ? "text" : "password"}
								placeholder="Enter your password"
								{...register("password")}
								disabled={isSubmitting}
								className={errors.password ? "border-red-500 pr-10" : "pr-10"}
								autoComplete="current-password"
							/>
							<Button
								type="button"
								variant="ghost"
								size="sm"
								className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
								onClick={() => setShowPassword(!showPassword)}
								disabled={isSubmitting}
							>
								{showPassword ? (
									<EyeOff className="h-4 w-4" />
								) : (
									<Eye className="h-4 w-4" />
								)}
							</Button>
						</div>
						{errors.password && (
							<p className="text-sm text-red-500">{errors.password.message}</p>
						)}
					</div>

					<Button type="submit" className="w-full" disabled={isSubmitting}>
						{isSubmitting ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Signing In...
							</>
						) : (
							"Sign In"
						)}
					</Button>
				</form>

				<div className="mt-4 text-center text-sm">
					Don't have an account?{" "}
					<Button
						variant="link"
						className="p-0 h-auto font-normal"
						onClick={() => router.push("/auth/register")}
						disabled={isSubmitting}
					>
						Create one here
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}
