"use client";

import {
	<PERSON><PERSON><PERSON>,
	Calendar,
	CheckCircle,
	Clock,
	PlayCircle,
	TrendingUp,
	Trophy,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useSession } from "@/lib/auth-client";

export default function StudentDashboard() {
	const { data: session } = useSession();

	// Mock data - will be replaced with real API calls
	const stats = {
		coursesEnrolled: 4,
		coursesCompleted: 1,
		totalXP: 1250,
		currentLevel: 5,
		streakDays: 7,
		assignmentsDue: 3,
	};

	const recentCourses = [
		{
			id: 1,
			title: "Mathematics - Algebra",
			progress: 75,
			nextLesson: "Quadratic Equations",
			dueDate: "Tomorrow",
			status: "in-progress",
		},
		{
			id: 2,
			title: "Science - Physics",
			progress: 45,
			nextLesson: "Newton's Laws",
			dueDate: "In 2 days",
			status: "in-progress",
		},
		{
			id: 3,
			title: "English - Literature",
			progress: 100,
			nextLesson: "Course Complete",
			dueDate: "Completed",
			status: "completed",
		},
	];

	const upcomingAssignments = [
		{
			id: 1,
			title: "Math Quiz - Quadratic Equations",
			subject: "Mathematics",
			dueDate: "Today, 11:59 PM",
			priority: "high",
		},
		{
			id: 2,
			title: "Physics Lab Report",
			subject: "Physics",
			dueDate: "Tomorrow, 5:00 PM",
			priority: "medium",
		},
		{
			id: 3,
			title: "English Essay - Character Analysis",
			subject: "English",
			dueDate: "In 3 days",
			priority: "low",
		},
	];

	const recentAchievements = [
		{
			id: 1,
			title: "Week Warrior",
			description: "7-day learning streak",
			icon: "🔥",
			earnedAt: "Today",
		},
		{
			id: 2,
			title: "Quiz Master",
			description: "Scored 100% on 5 quizzes",
			icon: "🎯",
			earnedAt: "Yesterday",
		},
	];

	return (
		<div className="p-6 space-y-6">
			{/* Welcome Section */}
			<div className="mb-8">
				<h1 className="text-3xl font-bold text-gray-900">
					Welcome back, {session?.user?.name || "Student"}! 👋
				</h1>
				<p className="text-gray-600 mt-2">
					Ready to continue your learning journey? Let's see what's next.
				</p>
			</div>

			{/* Stats Overview */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				<Card>
					<CardContent className="p-6">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-gray-600">Courses</p>
								<p className="text-2xl font-bold text-gray-900">
									{stats.coursesEnrolled}
								</p>
								<p className="text-xs text-gray-500">
									{stats.coursesCompleted} completed
								</p>
							</div>
							<BookOpen className="h-8 w-8 text-blue-600" />
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardContent className="p-6">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-gray-600">Total XP</p>
								<p className="text-2xl font-bold text-gray-900">
									{stats.totalXP}
								</p>
								<p className="text-xs text-gray-500">
									Level {stats.currentLevel}
								</p>
							</div>
							<Trophy className="h-8 w-8 text-yellow-600" />
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardContent className="p-6">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-gray-600">Streak</p>
								<p className="text-2xl font-bold text-gray-900">
									{stats.streakDays}
								</p>
								<p className="text-xs text-gray-500">days</p>
							</div>
							<TrendingUp className="h-8 w-8 text-green-600" />
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardContent className="p-6">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-gray-600">Due Soon</p>
								<p className="text-2xl font-bold text-gray-900">
									{stats.assignmentsDue}
								</p>
								<p className="text-xs text-gray-500">assignments</p>
							</div>
							<Clock className="h-8 w-8 text-red-600" />
						</div>
					</CardContent>
				</Card>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Recent Courses */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<BookOpen className="h-5 w-5" />
							Continue Learning
						</CardTitle>
						<CardDescription>Pick up where you left off</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						{recentCourses.map((course) => (
							<div key={course.id} className="border rounded-lg p-4">
								<div className="flex items-center justify-between mb-2">
									<h4 className="font-medium text-gray-900">{course.title}</h4>
									{course.status === "completed" ? (
										<CheckCircle className="h-5 w-5 text-green-600" />
									) : (
										<PlayCircle className="h-5 w-5 text-blue-600" />
									)}
								</div>
								<div className="space-y-2">
									<div className="flex justify-between text-sm text-gray-600">
										<span>Progress</span>
										<span>{course.progress}%</span>
									</div>
									<Progress value={course.progress} className="h-2" />
									<div className="flex justify-between items-center">
										<span className="text-sm text-gray-600">
											Next: {course.nextLesson}
										</span>
										<Button size="sm" variant="outline">
											Continue
										</Button>
									</div>
								</div>
							</div>
						))}
					</CardContent>
				</Card>

				{/* Upcoming Assignments */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Calendar className="h-5 w-5" />
							Upcoming Assignments
						</CardTitle>
						<CardDescription>Don't miss these deadlines</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						{upcomingAssignments.map((assignment) => (
							<div key={assignment.id} className="border rounded-lg p-4">
								<div className="flex items-start justify-between">
									<div className="flex-1">
										<h4 className="font-medium text-gray-900">
											{assignment.title}
										</h4>
										<p className="text-sm text-gray-600">
											{assignment.subject}
										</p>
										<div className="flex items-center gap-2 mt-2">
											<Clock className="h-4 w-4 text-gray-400" />
											<span className="text-sm text-gray-600">
												{assignment.dueDate}
											</span>
											<Badge
												variant={
													assignment.priority === "high"
														? "destructive"
														: assignment.priority === "medium"
															? "default"
															: "secondary"
												}
											>
												{assignment.priority}
											</Badge>
										</div>
									</div>
									<Button size="sm">Start</Button>
								</div>
							</div>
						))}
					</CardContent>
				</Card>
			</div>

			{/* Recent Achievements */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Trophy className="h-5 w-5" />
						Recent Achievements
					</CardTitle>
					<CardDescription>Celebrate your progress!</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						{recentAchievements.map((achievement) => (
							<div
								key={achievement.id}
								className="flex items-center gap-4 p-4 border rounded-lg"
							>
								<div className="text-2xl">{achievement.icon}</div>
								<div className="flex-1">
									<h4 className="font-medium text-gray-900">
										{achievement.title}
									</h4>
									<p className="text-sm text-gray-600">
										{achievement.description}
									</p>
									<p className="text-xs text-gray-500 mt-1">
										{achievement.earnedAt}
									</p>
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
