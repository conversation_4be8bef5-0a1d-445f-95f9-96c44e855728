"use client";

import {
	<PERSON><PERSON>hart<PERSON>,
	Bell,
	BookOpen,
	Calendar,
	Home,
	LogOut,
	Menu,
	Search,
	Trophy,
	User,
	X,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { signOut, useSession } from "@/lib/auth-client";

interface StudentLayoutProps {
	children: React.ReactNode;
}

const navigation = [
	{ name: "Dashboard", href: "/dashboard", icon: Home },
	{ name: "My Courses", href: "/courses", icon: BookOpen },
	{ name: "Assignments", href: "/assignments", icon: Calendar },
	{ name: "Progress", href: "/progress", icon: BarChart3 },
	{ name: "Achievements", href: "/achievements", icon: Trophy },
	{ name: "Profile", href: "/profile", icon: User },
];

export default function StudentLayout({ children }: StudentLayoutProps) {
	const [sidebarOpen, setSidebarOpen] = useState(false);
	const { data: session } = useSession();
	const router = useRouter();

	const handleSignOut = async () => {
		try {
			await signOut();
			router.push("/auth/login");
		} catch (error) {
			console.error("Sign out error:", error);
		}
	};

	return (
		<div className="min-h-screen bg-gray-50">
			{/* Mobile sidebar */}
			<div
				className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? "" : "hidden"}`}
			>
				<div
					className="fixed inset-0 bg-gray-600 bg-opacity-75"
					onClick={() => setSidebarOpen(false)}
					onKeyDown={(e) => {
						if (e.key === "Escape") {
							setSidebarOpen(false);
						}
					}}
					role="button"
					tabIndex={0}
					aria-label="Close sidebar"
				/>
				<div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white">
					<div className="flex h-16 items-center justify-between px-4">
						<div className="flex items-center">
							<div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
								<span className="text-white font-bold text-sm">L</span>
							</div>
							<span className="ml-3 text-xl font-semibold text-gray-800">
								LearnFunda
							</span>
						</div>
						<Button
							variant="ghost"
							size="sm"
							onClick={() => setSidebarOpen(false)}
						>
							<X className="h-5 w-5" />
						</Button>
					</div>
					<nav className="flex-1 space-y-1 px-2 py-4">
						{navigation.map((item) => (
							<a
								key={item.name}
								href={item.href}
								className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"
							>
								<item.icon className="mr-3 h-5 w-5" />
								{item.name}
							</a>
						))}
					</nav>
				</div>
			</div>

			{/* Desktop sidebar */}
			<div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
				<div className="flex flex-col flex-grow bg-white border-r border-gray-200">
					<div className="flex h-16 items-center px-4">
						<div className="flex items-center">
							<div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
								<span className="text-white font-bold text-sm">L</span>
							</div>
							<span className="ml-3 text-xl font-semibold text-gray-800">
								LearnFunda
							</span>
						</div>
					</div>
					<nav className="flex-1 space-y-1 px-2 py-4">
						{navigation.map((item) => (
							<a
								key={item.name}
								href={item.href}
								className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"
							>
								<item.icon className="mr-3 h-5 w-5" />
								{item.name}
							</a>
						))}
					</nav>

					{/* User section */}
					<div className="flex-shrink-0 border-t border-gray-200 p-4">
						<div className="flex items-center">
							<div className="flex-shrink-0">
								<div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
									<User className="h-4 w-4 text-blue-600" />
								</div>
							</div>
							<div className="ml-3 flex-1">
								<p className="text-sm font-medium text-gray-700">
									{session?.user?.name || "Student"}
								</p>
								<p className="text-xs text-gray-500">Student</p>
							</div>
							<Button variant="ghost" size="sm" onClick={handleSignOut}>
								<LogOut className="h-4 w-4" />
							</Button>
						</div>
					</div>
				</div>
			</div>

			{/* Main content */}
			<div className="lg:pl-64">
				{/* Top header */}
				<div className="sticky top-0 z-40 bg-white shadow-sm border-b border-gray-200">
					<div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
						<div className="flex items-center">
							<Button
								variant="ghost"
								size="sm"
								className="lg:hidden"
								onClick={() => setSidebarOpen(true)}
							>
								<Menu className="h-5 w-5" />
							</Button>

							{/* Search bar */}
							<div className="ml-4 flex-1 max-w-md">
								<div className="relative">
									<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
									<Input
										type="search"
										placeholder="Search courses, topics..."
										className="pl-10"
									/>
								</div>
							</div>
						</div>

						<div className="flex items-center space-x-4">
							{/* Notifications */}
							<Button variant="ghost" size="sm" className="relative">
								<Bell className="h-5 w-5" />
								<Badge className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs">
									3
								</Badge>
							</Button>

							{/* User menu - mobile */}
							<div className="lg:hidden">
								<Button variant="ghost" size="sm" onClick={handleSignOut}>
									<LogOut className="h-5 w-5" />
								</Button>
							</div>
						</div>
					</div>
				</div>

				{/* Page content */}
				<main className="flex-1">{children}</main>
			</div>
		</div>
	);
}
