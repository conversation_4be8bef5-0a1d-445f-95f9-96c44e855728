import { authRouter } from "@/server/api/routers/auth";
import { coursesRouter } from "@/server/api/routers/courses";
import { gamificationRouter } from "@/server/api/routers/gamification";
import { profileRouter } from "@/server/api/routers/profile";
import { progressRouter } from "@/server/api/routers/progress";
import { rolesRouter } from "@/server/api/routers/roles";
import { createCallerFactory, createTRPCRouter } from "@/server/api/trpc";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
	auth: authRouter,
	roles: rolesRouter,
	courses: coursesRouter,
	progress: progressRouter,
	gamification: gamificationRouter,
	profile: profileRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
