import { TRPCError } from "@trpc/server";
import { z } from "zod";
import {
	assignRoleToUser,
	getUserPrimaryRole,
	getUserSubdomain,
	getUserWithRoles,
	hasRole,
	removeRoleFromUser,
} from "@/lib/auth/roles";
import { roleTypeEnum } from "@/lib/db/schema/roles";
import {
	adminProcedure,
	createTRPCRouter,
	protectedProcedure,
	teacherOrAdminProcedure,
} from "@/server/api/trpc";

// Validation schemas
const assignRoleSchema = z.object({
	userId: z.string().min(1, "User ID is required"),
	roleName: z.enum(roleTypeEnum.enumValues),
});

const removeRoleSchema = z.object({
	userId: z.string().min(1, "User ID is required"),
	roleName: z.enum(roleTypeEnum.enumValues),
});

const getUserRolesSchema = z.object({
	userId: z.string().min(1, "User ID is required"),
});

export const rolesRouter = createTRPCRouter({
	// Get current user's roles
	getMyRoles: protectedProcedure.query(async ({ ctx }) => {
		try {
			const userWithRoles = await getUserWithRoles(ctx.user.id);

			if (!userWithRoles) {
				return {
					roles: [],
					primaryRole: null,
					subdomain: null,
				};
			}

			const primaryRole = await getUserPrimaryRole(ctx.user.id);
			const subdomain = await getUserSubdomain(ctx.user.id);

			return {
				roles: userWithRoles.roles,
				primaryRole,
				subdomain,
			};
		} catch (error) {
			console.error("Error fetching user roles:", error);
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to fetch user roles",
			});
		}
	}),

	// Get user roles by ID (admin/teacher only)
	getUserRoles: teacherOrAdminProcedure
		.input(getUserRolesSchema)
		.query(async ({ input }) => {
			try {
				const userWithRoles = await getUserWithRoles(input.userId);

				if (!userWithRoles) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "User not found",
					});
				}

				const primaryRole = await getUserPrimaryRole(input.userId);
				const subdomain = await getUserSubdomain(input.userId);

				return {
					user: {
						id: userWithRoles.id,
						name: userWithRoles.name,
						email: userWithRoles.email,
					},
					roles: userWithRoles.roles,
					primaryRole,
					subdomain,
				};
			} catch (error) {
				console.error("Error fetching user roles:", error);

				if (error instanceof TRPCError) {
					throw error;
				}

				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to fetch user roles",
				});
			}
		}),

	// Assign role to user (admin only)
	assignRole: adminProcedure
		.input(assignRoleSchema)
		.mutation(async ({ input, ctx }) => {
			try {
				const success = await assignRoleToUser(
					input.userId,
					input.roleName,
					ctx.user.id,
				);

				if (!success) {
					throw new TRPCError({
						code: "INTERNAL_SERVER_ERROR",
						message: "Failed to assign role",
					});
				}

				return {
					success: true,
					message: `Role ${input.roleName} assigned successfully`,
				};
			} catch (error) {
				console.error("Error assigning role:", error);

				if (error instanceof TRPCError) {
					throw error;
				}

				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to assign role",
				});
			}
		}),

	// Remove role from user (admin only)
	removeRole: adminProcedure
		.input(removeRoleSchema)
		.mutation(async ({ input }) => {
			try {
				const success = await removeRoleFromUser(input.userId, input.roleName);

				if (!success) {
					throw new TRPCError({
						code: "INTERNAL_SERVER_ERROR",
						message: "Failed to remove role",
					});
				}

				return {
					success: true,
					message: `Role ${input.roleName} removed successfully`,
				};
			} catch (error) {
				console.error("Error removing role:", error);

				if (error instanceof TRPCError) {
					throw error;
				}

				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to remove role",
				});
			}
		}),

	// Check if current user has specific role
	hasRole: protectedProcedure
		.input(
			z.object({
				roleName: z.enum(roleTypeEnum.enumValues),
			}),
		)
		.query(async ({ input, ctx }) => {
			try {
				const hasUserRole = await hasRole(ctx.user.id, input.roleName);
				return { hasRole: hasUserRole };
			} catch (error) {
				console.error("Error checking user role:", error);
				return { hasRole: false };
			}
		}),

	// Get user's appropriate subdomain
	getMySubdomain: protectedProcedure.query(async ({ ctx }) => {
		try {
			const subdomain = await getUserSubdomain(ctx.user.id);
			return { subdomain };
		} catch (error) {
			console.error("Error getting user subdomain:", error);
			return { subdomain: null };
		}
	}),
});
