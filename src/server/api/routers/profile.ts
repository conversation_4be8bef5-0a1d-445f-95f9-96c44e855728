import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { userProfiles } from "@/lib/db/schema/users";
import {
	createTRPCRouter,
	protectedProcedure,
	studentProcedure,
} from "@/server/api/trpc";

// Validation schemas
const updateProfileSchema = z.object({
	fullName: z.string().min(1, "Full name is required").max(255),
	standard: z.enum(["8th", "9th", "10th"]),
	schoolName: z.string().min(1, "School name is required").max(255),
	educationalBoard: z.enum(["CBSE", "ICSE", "STATE_BOARD"]),
	interests: z.array(z.string()).optional(),
	dislikes: z.array(z.string()).optional(),
});

const createProfileSchema = z.object({
	fullName: z.string().min(1, "Full name is required").max(255),
	standard: z.enum(["8th", "9th", "10th"]),
	schoolName: z.string().min(1, "School name is required").max(255),
	educationalBoard: z.enum(["CBSE", "ICSE", "STATE_BOARD"]),
	interests: z.array(z.string()).optional(),
	dislikes: z.array(z.string()).optional(),
});

export const profileRouter = createTRPCRouter({
	// Get current user's profile
	getMyProfile: protectedProcedure.query(async ({ ctx }) => {
		try {
			const profile = await ctx.db
				.select()
				.from(userProfiles)
				.where(eq(userProfiles.userId, ctx.user.id))
				.limit(1);

			if (!profile.length) {
				return null;
			}

			const profileData = profile[0];

			// Parse JSON fields
			return {
				...profileData,
				interests: profileData.interests
					? JSON.parse(profileData.interests)
					: [],
				dislikes: profileData.dislikes ? JSON.parse(profileData.dislikes) : [],
			};
		} catch (error) {
			console.error("Error fetching user profile:", error);
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to fetch profile",
			});
		}
	}),

	// Create user profile (for first-time setup)
	createProfile: protectedProcedure
		.input(createProfileSchema)
		.mutation(async ({ ctx, input }) => {
			try {
				// Check if profile already exists
				const existingProfile = await ctx.db
					.select()
					.from(userProfiles)
					.where(eq(userProfiles.userId, ctx.user.id))
					.limit(1);

				if (existingProfile.length > 0) {
					throw new TRPCError({
						code: "CONFLICT",
						message: "Profile already exists. Use update instead.",
					});
				}

				// Create new profile
				const newProfile = await ctx.db
					.insert(userProfiles)
					.values({
						userId: ctx.user.id,
						fullName: input.fullName,
						standard: input.standard,
						schoolName: input.schoolName,
						educationalBoard: input.educationalBoard,
						interests: input.interests ? JSON.stringify(input.interests) : null,
						dislikes: input.dislikes ? JSON.stringify(input.dislikes) : null,
						profileCompleted: true,
					})
					.returning();

				const profileData = newProfile[0];

				return {
					...profileData,
					interests: profileData.interests
						? JSON.parse(profileData.interests)
						: [],
					dislikes: profileData.dislikes
						? JSON.parse(profileData.dislikes)
						: [],
				};
			} catch (error) {
				console.error("Error creating user profile:", error);

				if (error instanceof TRPCError) {
					throw error;
				}

				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to create profile",
				});
			}
		}),

	// Update user profile
	updateProfile: protectedProcedure
		.input(updateProfileSchema)
		.mutation(async ({ ctx, input }) => {
			try {
				// Check if profile exists
				const existingProfile = await ctx.db
					.select()
					.from(userProfiles)
					.where(eq(userProfiles.userId, ctx.user.id))
					.limit(1);

				if (!existingProfile.length) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "Profile not found. Create profile first.",
					});
				}

				// Update profile
				const updatedProfile = await ctx.db
					.update(userProfiles)
					.set({
						fullName: input.fullName,
						standard: input.standard,
						schoolName: input.schoolName,
						educationalBoard: input.educationalBoard,
						interests: input.interests ? JSON.stringify(input.interests) : null,
						dislikes: input.dislikes ? JSON.stringify(input.dislikes) : null,
						profileCompleted: true,
						updatedAt: new Date(),
					})
					.where(eq(userProfiles.userId, ctx.user.id))
					.returning();

				const profileData = updatedProfile[0];

				return {
					...profileData,
					interests: profileData.interests
						? JSON.parse(profileData.interests)
						: [],
					dislikes: profileData.dislikes
						? JSON.parse(profileData.dislikes)
						: [],
				};
			} catch (error) {
				console.error("Error updating user profile:", error);

				if (error instanceof TRPCError) {
					throw error;
				}

				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to update profile",
				});
			}
		}),

	// Check if profile is completed
	isProfileCompleted: protectedProcedure.query(async ({ ctx }) => {
		try {
			const profile = await ctx.db
				.select({ profileCompleted: userProfiles.profileCompleted })
				.from(userProfiles)
				.where(eq(userProfiles.userId, ctx.user.id))
				.limit(1);

			return {
				completed: profile.length > 0 ? profile[0].profileCompleted : false,
				exists: profile.length > 0,
			};
		} catch (error) {
			console.error("Error checking profile completion:", error);
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to check profile status",
			});
		}
	}),

	// Get profile completion stats
	getProfileStats: studentProcedure.query(async ({ ctx }) => {
		try {
			const profile = await ctx.db
				.select()
				.from(userProfiles)
				.where(eq(userProfiles.userId, ctx.user.id))
				.limit(1);

			if (!profile.length) {
				return {
					completionPercentage: 0,
					missingFields: [
						"fullName",
						"standard",
						"schoolName",
						"educationalBoard",
					],
					isComplete: false,
				};
			}

			const profileData = profile[0];
			const requiredFields = [
				"fullName",
				"standard",
				"schoolName",
				"educationalBoard",
			];
			const optionalFields = ["interests", "dislikes"];

			const missingRequired = requiredFields.filter(
				(field) => !profileData[field as keyof typeof profileData],
			);
			const missingOptional = optionalFields.filter(
				(field) => !profileData[field as keyof typeof profileData],
			);

			const totalFields = requiredFields.length + optionalFields.length;
			const completedFields =
				totalFields - missingRequired.length - missingOptional.length;
			const completionPercentage = Math.round(
				(completedFields / totalFields) * 100,
			);

			return {
				completionPercentage,
				missingFields: [...missingRequired, ...missingOptional],
				isComplete: missingRequired.length === 0,
				profileCompleted: profileData.profileCompleted,
			};
		} catch (error) {
			console.error("Error getting profile stats:", error);
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to get profile statistics",
			});
		}
	}),
});
