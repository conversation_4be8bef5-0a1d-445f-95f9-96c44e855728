import { TRPCError } from "@trpc/server";
import { and, count, eq, sql } from "drizzle-orm";
import { z } from "zod";
import { subjects, topics } from "@/lib/db/schema/content";
import { userTopicProgress, userVideoProgress } from "@/lib/db/schema/progress";
import { userProfiles } from "@/lib/db/schema/users";
import { createTRPCRouter, studentProcedure } from "@/server/api/trpc";

// Validation schemas
const getCourseProgressSchema = z.object({
	subjectId: z.string().uuid(),
});

const updateVideoProgressSchema = z.object({
	videoId: z.string().uuid(),
	watchedDuration: z.number().min(0),
	totalDuration: z.number().positive(),
	isCompleted: z.boolean().optional(),
});

export const progressRouter = createTRPCRouter({
	// Get overall user progress statistics
	getUserProgress: studentProcedure.query(async ({ ctx }) => {
		try {
			// Get user profile for standard and board
			const userProfile = await ctx.db
				.select()
				.from(userProfiles)
				.where(eq(userProfiles.userId, ctx.user.id))
				.limit(1);

			if (!userProfile.length) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message:
						"User profile not found. Please complete your profile first.",
				});
			}

			const profile = userProfile[0];

			// Get total available courses for user's standard and board
			const totalCourses = await ctx.db
				.select({ count: count() })
				.from(subjects)
				.where(
					and(
						eq(subjects.standard, profile.standard),
						eq(subjects.educationalBoard, profile.educationalBoard),
						eq(subjects.isActive, true),
					),
				);

			// Get courses with progress
			const coursesWithProgress = await ctx.db
				.select({
					subjectId: subjects.id,
					subjectName: subjects.name,
					totalTopics: count(topics.id),
				})
				.from(subjects)
				.leftJoin(topics, eq(topics.subjectId, subjects.id))
				.where(
					and(
						eq(subjects.standard, profile.standard),
						eq(subjects.educationalBoard, profile.educationalBoard),
						eq(subjects.isActive, true),
					),
				)
				.groupBy(subjects.id, subjects.name);

			// Get completed topics count for each subject
			const completedTopicsQuery = await ctx.db
				.select({
					subjectId: topics.subjectId,
					completedTopics: count(userTopicProgress.id),
				})
				.from(userTopicProgress)
				.innerJoin(topics, eq(topics.id, userTopicProgress.topicId))
				.innerJoin(subjects, eq(subjects.id, topics.subjectId))
				.where(
					and(
						eq(userTopicProgress.userId, ctx.user.id),
						eq(userTopicProgress.status, "COMPLETED"),
						eq(subjects.standard, profile.standard),
						eq(subjects.educationalBoard, profile.educationalBoard),
					),
				)
				.groupBy(topics.subjectId);

			// Calculate course completion stats
			let coursesEnrolled = 0;
			let coursesCompleted = 0;

			for (const course of coursesWithProgress) {
				if (course.totalTopics > 0) {
					coursesEnrolled++;
					const completedForCourse = completedTopicsQuery.find(
						(c) => c.subjectId === course.subjectId,
					);
					if (
						completedForCourse &&
						completedForCourse.completedTopics >= course.totalTopics
					) {
						coursesCompleted++;
					}
				}
			}

			// Get total time spent learning
			const totalTimeSpent = await ctx.db
				.select({
					totalTime: sql<number>`COALESCE(SUM(${userTopicProgress.timeSpent}), 0)`,
				})
				.from(userTopicProgress)
				.where(eq(userTopicProgress.userId, ctx.user.id));

			return {
				coursesEnrolled,
				coursesCompleted,
				totalCourses: totalCourses[0]?.count || 0,
				totalTimeSpent: totalTimeSpent[0]?.totalTime || 0,
				completionRate:
					coursesEnrolled > 0
						? Math.round((coursesCompleted / coursesEnrolled) * 100)
						: 0,
			};
		} catch (error) {
			console.error("Error fetching user progress:", error);

			if (error instanceof TRPCError) {
				throw error;
			}

			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to fetch user progress",
			});
		}
	}),

	// Get progress for a specific course
	getCourseProgress: studentProcedure
		.input(getCourseProgressSchema)
		.query(async ({ ctx, input }) => {
			try {
				// Get course details
				const course = await ctx.db
					.select()
					.from(subjects)
					.where(
						and(eq(subjects.id, input.subjectId), eq(subjects.isActive, true)),
					)
					.limit(1);

				if (!course.length) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "Course not found",
					});
				}

				// Get topics with progress
				const topicsWithProgress = await ctx.db
					.select({
						id: topics.id,
						title: topics.name,
						description: topics.description,
						sortOrder: topics.sortOrder,
						status: userTopicProgress.status,
						progressPercentage: userTopicProgress.progressPercentage,
						timeSpent: userTopicProgress.timeSpent,
						lastAccessedAt: userTopicProgress.lastAccessedAt,
						completedAt: userTopicProgress.completedAt,
					})
					.from(topics)
					.leftJoin(
						userTopicProgress,
						and(
							eq(userTopicProgress.topicId, topics.id),
							eq(userTopicProgress.userId, ctx.user.id),
						),
					)
					.where(
						and(
							eq(topics.subjectId, input.subjectId),
							eq(topics.isActive, true),
						),
					)
					.orderBy(topics.sortOrder, topics.name);

				// Calculate overall course progress
				const totalTopics = topicsWithProgress.length;
				const completedTopics = topicsWithProgress.filter(
					(topic) => topic.status === "COMPLETED",
				).length;
				const inProgressTopics = topicsWithProgress.filter(
					(topic) => topic.status === "IN_PROGRESS",
				).length;

				const overallProgress =
					totalTopics > 0
						? Math.round((completedTopics / totalTopics) * 100)
						: 0;

				return {
					course: course[0],
					topics: topicsWithProgress,
					stats: {
						totalTopics,
						completedTopics,
						inProgressTopics,
						notStartedTopics: totalTopics - completedTopics - inProgressTopics,
						overallProgress,
					},
				};
			} catch (error) {
				console.error("Error fetching course progress:", error);

				if (error instanceof TRPCError) {
					throw error;
				}

				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to fetch course progress",
				});
			}
		}),

	// Get recent learning activity
	getRecentActivity: studentProcedure.query(async ({ ctx }) => {
		try {
			// Get recently accessed topics
			const recentTopics = await ctx.db
				.select({
					topicId: userTopicProgress.topicId,
					topicTitle: topics.name,
					subjectName: subjects.name,
					status: userTopicProgress.status,
					progressPercentage: userTopicProgress.progressPercentage,
					lastAccessedAt: userTopicProgress.lastAccessedAt,
					timeSpent: userTopicProgress.timeSpent,
				})
				.from(userTopicProgress)
				.innerJoin(topics, eq(topics.id, userTopicProgress.topicId))
				.innerJoin(subjects, eq(subjects.id, topics.subjectId))
				.where(eq(userTopicProgress.userId, ctx.user.id))
				.orderBy(sql`${userTopicProgress.lastAccessedAt} DESC`)
				.limit(10);

			return {
				recentTopics,
			};
		} catch (error) {
			console.error("Error fetching recent activity:", error);
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to fetch recent activity",
			});
		}
	}),

	// Update video progress
	updateVideoProgress: studentProcedure
		.input(updateVideoProgressSchema)
		.mutation(async ({ ctx, input }) => {
			try {
				const progressPercentage = Math.min(
					100,
					Math.round((input.watchedDuration / input.totalDuration) * 100),
				);

				// Upsert video progress
				await ctx.db
					.insert(userVideoProgress)
					.values({
						userId: ctx.user.id,
						videoId: input.videoId,
						watchedDuration: input.watchedDuration,
						totalDuration: input.totalDuration,
						progressPercentage: progressPercentage.toString(),
						isCompleted: input.isCompleted || progressPercentage >= 90,
						watchCount: 1,
					})
					.onConflictDoUpdate({
						target: [userVideoProgress.userId, userVideoProgress.videoId],
						set: {
							watchedDuration: input.watchedDuration,
							progressPercentage: progressPercentage.toString(),
							isCompleted: input.isCompleted || progressPercentage >= 90,
							watchCount: sql`${userVideoProgress.watchCount} + 1`,
							updatedAt: sql`NOW()`,
						},
					});

				return {
					success: true,
					progressPercentage,
				};
			} catch (error) {
				console.error("Error updating video progress:", error);
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to update video progress",
				});
			}
		}),
});
