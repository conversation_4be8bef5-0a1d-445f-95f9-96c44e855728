import { TRPCError } from "@trpc/server";
import { and, desc, eq, sql } from "drizzle-orm";
import { z } from "zod";
import {
	badges,
	userBadges,
	userStreaks,
	userXp,
} from "@/lib/db/schema/gamification";
import {
	createTRPCRouter,
	protectedProcedure,
	studentProcedure,
} from "@/server/api/trpc";

// Validation schemas
const updateStreakSchema = z.object({
	streakType: z.enum([
		"DAILY_LOGIN",
		"VIDEO_COMPLETION",
		"QUIZ_COMPLETION",
		"NOTE_CREATION",
	]),
});

const awardXPSchema = z.object({
	amount: z.number().positive(),
	reason: z.string().min(1),
});

// XP level calculation utility
function calculateLevel(totalXP: number): number {
	// Level formula: Level = floor(sqrt(XP / 100))
	// This means: Level 1 = 100 XP, Level 2 = 400 XP, Level 3 = 900 XP, etc.
	return Math.floor(Math.sqrt(totalXP / 100)) + 1;
}

function getXPForLevel(level: number): number {
	// XP required for a specific level
	return (level - 1) ** 2 * 100;
}

function getXPForNextLevel(currentXP: number): {
	currentLevel: number;
	nextLevel: number;
	xpNeeded: number;
	xpProgress: number;
} {
	const currentLevel = calculateLevel(currentXP);
	const nextLevel = currentLevel + 1;
	const xpForCurrentLevel = getXPForLevel(currentLevel);
	const xpForNextLevel = getXPForLevel(nextLevel);
	const xpNeeded = xpForNextLevel - currentXP;
	const xpProgress = currentXP - xpForCurrentLevel;

	return {
		currentLevel,
		nextLevel,
		xpNeeded,
		xpProgress,
	};
}

export const gamificationRouter = createTRPCRouter({
	// Get user's gamification stats (XP, level, badges, streaks)
	getUserStats: studentProcedure.query(async ({ ctx }) => {
		try {
			// Get user XP
			const userXPData = await ctx.db
				.select()
				.from(userXp)
				.where(eq(userXp.userId, ctx.user.id))
				.limit(1);

			const totalXP = userXPData[0]?.totalXp || 0;
			const levelInfo = getXPForNextLevel(totalXP);

			// Get user streaks
			const streaks = await ctx.db
				.select()
				.from(userStreaks)
				.where(
					and(
						eq(userStreaks.userId, ctx.user.id),
						eq(userStreaks.isActive, true),
					),
				);

			// Get user badges count
			const badgeCount = await ctx.db
				.select({ count: sql<number>`COUNT(*)` })
				.from(userBadges)
				.where(eq(userBadges.userId, ctx.user.id));

			// Get recent badges (last 5)
			const recentBadges = await ctx.db
				.select({
					id: userBadges.id,
					badgeId: userBadges.badgeId,
					earnedAt: userBadges.earnedAt,
					name: badges.name,
					description: badges.description,
					iconUrl: badges.iconUrl,
					badgeType: badges.badgeType,
				})
				.from(userBadges)
				.innerJoin(badges, eq(badges.id, userBadges.badgeId))
				.where(eq(userBadges.userId, ctx.user.id))
				.orderBy(desc(userBadges.earnedAt))
				.limit(5);

			// Calculate longest streak across all types
			const longestStreak = Math.max(...streaks.map((s) => s.longestStreak), 0);

			// Get current daily login streak
			const dailyStreak =
				streaks.find((s) => s.streakType === "DAILY_LOGIN")?.currentStreak || 0;

			return {
				xp: {
					total: totalXP,
					currentLevel: levelInfo.currentLevel,
					nextLevel: levelInfo.nextLevel,
					xpForNextLevel: levelInfo.xpNeeded,
					xpProgress: levelInfo.xpProgress,
					xpForCurrentLevel: getXPForLevel(levelInfo.currentLevel),
					xpForNext: getXPForLevel(levelInfo.nextLevel),
				},
				streaks: {
					daily: dailyStreak,
					longest: longestStreak,
					all: streaks,
				},
				badges: {
					total: badgeCount[0]?.count || 0,
					recent: recentBadges,
				},
			};
		} catch (error) {
			console.error("Error fetching user gamification stats:", error);
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to fetch user stats",
			});
		}
	}),

	// Get all user badges
	getUserBadges: studentProcedure.query(async ({ ctx }) => {
		try {
			const userBadgesList = await ctx.db
				.select({
					id: userBadges.id,
					earnedAt: userBadges.earnedAt,
					progress: userBadges.progress,
					badgeId: badges.id,
					name: badges.name,
					description: badges.description,
					badgeType: badges.badgeType,
					iconUrl: badges.iconUrl,
					xpReward: badges.xpReward,
				})
				.from(userBadges)
				.innerJoin(badges, eq(badges.id, userBadges.badgeId))
				.where(eq(userBadges.userId, ctx.user.id))
				.orderBy(desc(userBadges.earnedAt));

			return userBadgesList;
		} catch (error) {
			console.error("Error fetching user badges:", error);
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to fetch user badges",
			});
		}
	}),

	// Get available badges (not yet earned)
	getAvailableBadges: studentProcedure.query(async ({ ctx }) => {
		try {
			const availableBadges = await ctx.db
				.select()
				.from(badges)
				.where(
					and(
						eq(badges.isActive, true),
						sql`${badges.id} NOT IN (
							SELECT ${userBadges.badgeId} 
							FROM ${userBadges} 
							WHERE ${userBadges.userId} = ${ctx.user.id}
						)`,
					),
				)
				.orderBy(badges.badgeType, badges.name);

			return availableBadges;
		} catch (error) {
			console.error("Error fetching available badges:", error);
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to fetch available badges",
			});
		}
	}),

	// Update user streak
	updateStreak: studentProcedure
		.input(updateStreakSchema)
		.mutation(async ({ ctx, input }) => {
			try {
				const today = new Date();
				today.setHours(0, 0, 0, 0);

				// Get current streak
				const currentStreak = await ctx.db
					.select()
					.from(userStreaks)
					.where(
						and(
							eq(userStreaks.userId, ctx.user.id),
							eq(userStreaks.streakType, input.streakType),
						),
					)
					.limit(1);

				if (currentStreak.length === 0) {
					// Create new streak
					await ctx.db.insert(userStreaks).values({
						userId: ctx.user.id,
						streakType: input.streakType,
						currentStreak: 1,
						longestStreak: 1,
						lastActivityDate: today,
						isActive: true,
					});

					return { currentStreak: 1, longestStreak: 1 };
				} else {
					const streak = currentStreak[0];
					const lastActivity = streak.lastActivityDate;

					if (!lastActivity) {
						// First activity
						await ctx.db
							.update(userStreaks)
							.set({
								currentStreak: 1,
								longestStreak: Math.max(1, streak.longestStreak),
								lastActivityDate: today,
								updatedAt: new Date(),
							})
							.where(eq(userStreaks.id, streak.id));

						return {
							currentStreak: 1,
							longestStreak: Math.max(1, streak.longestStreak),
						};
					}

					const daysDiff = Math.floor(
						(today.getTime() - lastActivity.getTime()) / (1000 * 60 * 60 * 24),
					);

					if (daysDiff === 0) {
						// Same day, no change
						return {
							currentStreak: streak.currentStreak,
							longestStreak: streak.longestStreak,
						};
					} else if (daysDiff === 1) {
						// Consecutive day, increment streak
						const newCurrentStreak = streak.currentStreak + 1;
						const newLongestStreak = Math.max(
							newCurrentStreak,
							streak.longestStreak,
						);

						await ctx.db
							.update(userStreaks)
							.set({
								currentStreak: newCurrentStreak,
								longestStreak: newLongestStreak,
								lastActivityDate: today,
								updatedAt: new Date(),
							})
							.where(eq(userStreaks.id, streak.id));

						return {
							currentStreak: newCurrentStreak,
							longestStreak: newLongestStreak,
						};
					} else {
						// Streak broken, reset to 1
						await ctx.db
							.update(userStreaks)
							.set({
								currentStreak: 1,
								lastActivityDate: today,
								updatedAt: new Date(),
							})
							.where(eq(userStreaks.id, streak.id));

						return { currentStreak: 1, longestStreak: streak.longestStreak };
					}
				}
			} catch (error) {
				console.error("Error updating streak:", error);
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to update streak",
				});
			}
		}),

	// Award XP to user
	awardXP: protectedProcedure
		.input(awardXPSchema)
		.mutation(async ({ ctx, input }) => {
			try {
				// Get current XP
				const currentXPData = await ctx.db
					.select()
					.from(userXp)
					.where(eq(userXp.userId, ctx.user.id))
					.limit(1);

				const currentTotal = currentXPData[0]?.totalXp || 0;
				const newTotal = currentTotal + input.amount;
				const oldLevel = calculateLevel(currentTotal);
				const newLevel = calculateLevel(newTotal);

				if (currentXPData.length === 0) {
					// Create new XP record
					await ctx.db.insert(userXp).values({
						userId: ctx.user.id,
						totalXp: newTotal,
						currentLevel: newLevel,
					});
				} else {
					// Update existing XP record
					await ctx.db
						.update(userXp)
						.set({
							totalXp: newTotal,
							currentLevel: newLevel,
							updatedAt: new Date(),
						})
						.where(eq(userXp.userId, ctx.user.id));
				}

				return {
					success: true,
					xpAwarded: input.amount,
					totalXP: newTotal,
					oldLevel,
					newLevel,
					leveledUp: newLevel > oldLevel,
				};
			} catch (error) {
				console.error("Error awarding XP:", error);
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to award XP",
				});
			}
		}),
});
