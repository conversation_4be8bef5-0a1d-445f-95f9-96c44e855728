import { TRPCError } from "@trpc/server";
import { and, eq } from "drizzle-orm";
import { z } from "zod";
import { quizzes, subjects, topics, videos } from "@/lib/db/schema/content";
import { userProfiles } from "@/lib/db/schema/users";
import {
	createTRPCRouter,
	protectedProcedure,
	studentProcedure,
} from "@/server/api/trpc";

// Validation schemas
const getCoursesByStandardSchema = z.object({
	standard: z.enum(["8th", "9th", "10th"]),
	educationalBoard: z.enum(["CBSE", "ICSE", "STATE_BOARD"]),
});

const getCourseDetailsSchema = z.object({
	subjectId: z.string().uuid(),
});

const getTopicContentSchema = z.object({
	topicId: z.string().uuid(),
});

export const coursesRouter = createTRPCRouter({
	// Get courses available for student's standard and board
	getMyCourses: studentProcedure.query(async ({ ctx }) => {
		try {
			// Get user profile to determine standard and board
			const userProfile = await ctx.db
				.select()
				.from(userProfiles)
				.where(eq(userProfiles.userId, ctx.user.id))
				.limit(1);

			if (!userProfile.length) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message:
						"User profile not found. Please complete your profile first.",
				});
			}

			const profile = userProfile[0];

			// Get subjects for user's standard and board
			const courses = await ctx.db
				.select({
					id: subjects.id,
					name: subjects.name,
					description: subjects.description,
					iconUrl: subjects.iconUrl,
					colorCode: subjects.colorCode,
					sortOrder: subjects.sortOrder,
				})
				.from(subjects)
				.where(
					and(
						eq(subjects.standard, profile.standard),
						eq(subjects.educationalBoard, profile.educationalBoard),
						eq(subjects.isActive, true),
					),
				)
				.orderBy(subjects.sortOrder, subjects.name);

			return {
				courses,
				userProfile: {
					standard: profile.standard,
					educationalBoard: profile.educationalBoard,
				},
			};
		} catch (error) {
			console.error("Error fetching user courses:", error);

			if (error instanceof TRPCError) {
				throw error;
			}

			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to fetch courses",
			});
		}
	}),

	// Get all available courses by standard and board
	getCoursesByStandard: protectedProcedure
		.input(getCoursesByStandardSchema)
		.query(async ({ ctx, input }) => {
			try {
				const courses = await ctx.db
					.select({
						id: subjects.id,
						name: subjects.name,
						description: subjects.description,
						iconUrl: subjects.iconUrl,
						colorCode: subjects.colorCode,
						sortOrder: subjects.sortOrder,
					})
					.from(subjects)
					.where(
						and(
							eq(subjects.standard, input.standard),
							eq(subjects.educationalBoard, input.educationalBoard),
							eq(subjects.isActive, true),
						),
					)
					.orderBy(subjects.sortOrder, subjects.name);

				return courses;
			} catch (error) {
				console.error("Error fetching courses by standard:", error);
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to fetch courses",
				});
			}
		}),

	// Get detailed course information with topics
	getCourseDetails: studentProcedure
		.input(getCourseDetailsSchema)
		.query(async ({ ctx, input }) => {
			try {
				// Get subject details
				const subject = await ctx.db
					.select()
					.from(subjects)
					.where(
						and(eq(subjects.id, input.subjectId), eq(subjects.isActive, true)),
					)
					.limit(1);

				if (!subject.length) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "Course not found",
					});
				}

				// Get topics for this subject
				const courseTopics = await ctx.db
					.select({
						id: topics.id,
						name: topics.name,
						description: topics.description,
						difficultyLevel: topics.difficultyLevel,
						estimatedDuration: topics.estimatedDuration,
						sortOrder: topics.sortOrder,
					})
					.from(topics)
					.where(
						and(
							eq(topics.subjectId, input.subjectId),
							eq(topics.isActive, true),
						),
					)
					.orderBy(topics.sortOrder, topics.name);

				return {
					subject: subject[0],
					topics: courseTopics,
				};
			} catch (error) {
				console.error("Error fetching course details:", error);

				if (error instanceof TRPCError) {
					throw error;
				}

				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to fetch course details",
				});
			}
		}),

	// Get topic content (videos and quizzes)
	getTopicContent: studentProcedure
		.input(getTopicContentSchema)
		.query(async ({ ctx, input }) => {
			try {
				// Get topic details
				const topic = await ctx.db
					.select()
					.from(topics)
					.where(and(eq(topics.id, input.topicId), eq(topics.isActive, true)))
					.limit(1);

				if (!topic.length) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "Topic not found",
					});
				}

				// Get videos for this topic
				const topicVideos = await ctx.db
					.select({
						id: videos.id,
						title: videos.title,
						description: videos.description,
						videoUrl: videos.videoUrl,
						thumbnailUrl: videos.thumbnailUrl,
						duration: videos.duration,
						sortOrder: videos.sortOrder,
					})
					.from(videos)
					.where(
						and(
							eq(videos.topicId, input.topicId),
							eq(videos.isActive, true),
							eq(videos.status, "PUBLISHED"),
						),
					)
					.orderBy(videos.sortOrder, videos.title);

				// Get quizzes for this topic
				const topicQuizzes = await ctx.db
					.select({
						id: quizzes.id,
						title: quizzes.title,
						description: quizzes.description,
						timeLimit: quizzes.timeLimit,
						passingScore: quizzes.passingScore,
						maxAttempts: quizzes.maxAttempts,
						sortOrder: quizzes.sortOrder,
					})
					.from(quizzes)
					.where(
						and(eq(quizzes.topicId, input.topicId), eq(quizzes.isActive, true)),
					)
					.orderBy(quizzes.sortOrder, quizzes.title);

				return {
					topic: topic[0],
					videos: topicVideos,
					quizzes: topicQuizzes,
				};
			} catch (error) {
				console.error("Error fetching topic content:", error);

				if (error instanceof TRPCError) {
					throw error;
				}

				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to fetch topic content",
				});
			}
		}),
});
