import { initTR<PERSON>, TRPCError } from "@trpc/server";
import type { FetchCreateContextFnOptions } from "@trpc/server/adapters/fetch";
import superjson from "superjson";
import { ZodError } from "zod";
import { auth } from "@/lib/auth";
import { getUserWithRoles } from "@/lib/auth/roles";
import type { RoleType, UserWithRoles } from "@/lib/auth/types";
import { db } from "@/lib/db";

/**
 * 1. CONTEXT
 *
 * This section defines the "contexts" that are available in the backend API.
 *
 * These allow you to access things when processing a request, like the database, the session, etc.
 *
 * This helper generates the "internals" for a tRPC context. The API handler and RSC clients each
 * wrap this and provides the required context.
 *
 * @see https://trpc.io/docs/server/context
 */
export const createTRPCContext = async (opts: FetchCreateContextFnOptions) => {
	const { req } = opts;

	// Get the session and user with roles from the server using the request
	const getUserWithRolesData = async (): Promise<UserWithRoles | null> => {
		try {
			const session = await auth.api.getSession({
				headers: req.headers as Headers,
			});

			if (!session?.user) {
				return null;
			}

			// Get user with roles
			const userWithRoles = await getUserWithRoles(session.user.id);
			return userWithRoles;
		} catch (error) {
			console.error("tRPC Context - Error getting session:", error);
			return null;
		}
	};

	const userWithRoles = await getUserWithRolesData();

	return {
		db,
		user: userWithRoles?.id
			? {
					id: userWithRoles.id,
					name: userWithRoles.name,
					email: userWithRoles.email,
					emailVerified: userWithRoles.emailVerified,
					image: userWithRoles.image,
				}
			: null,
		userWithRoles,
		req,
	};
};

/**
 * 2. INITIALIZATION
 *
 * This is where the tRPC API is initialized, connecting the context and transformer. We also parse
 * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation
 * errors on the backend.
 */
const t = initTRPC.context<typeof createTRPCContext>().create({
	transformer: superjson,
	errorFormatter({ shape, error }) {
		return {
			...shape,
			data: {
				...shape.data,
				zodError:
					error.cause instanceof ZodError ? error.cause.flatten() : null,
			},
		};
	},
});

/**
 * Create a server-side caller.
 *
 * @see https://trpc.io/docs/server/server-side-calls
 */
export const createCallerFactory = t.createCallerFactory;

/**
 * 3. ROUTER & PROCEDURE (THE IMPORTANT BIT)
 *
 * These are the pieces you use to build your tRPC API. You should import these a lot in the
 * "/src/server/api/routers" directory.
 */

/**
 * This is how you create new routers and sub-routers in your tRPC API.
 *
 * @see https://trpc.io/docs/router
 */
export const createTRPCRouter = t.router;

/**
 * Public (unauthenticated) procedure
 *
 * This is the base piece you use to build new queries and mutations on your tRPC API. It does not
 * guarantee that a user querying is authorized, but you can still access user session data if they
 * are logged in.
 */
export const publicProcedure = t.procedure;

/**
 * Protected (authenticated) procedure
 *
 * If you want a query or mutation to ONLY be accessible to logged in users, use this. It verifies
 * the session is valid and guarantees `ctx.user` is not null.
 *
 * @see https://trpc.io/docs/procedures
 */
export const protectedProcedure = t.procedure.use(({ ctx, next }) => {
	if (!ctx.user) {
		throw new TRPCError({ code: "UNAUTHORIZED" });
	}
	return next({
		ctx: {
			// infers the `user` as non-nullable
			...ctx,
			user: ctx.user,
		},
	});
});

/**
 * Role-based procedure factory
 * Creates procedures that require specific roles
 */
const createRoleProcedure = (requiredRoles: RoleType[]) => {
	return t.procedure.use(async ({ ctx, next }) => {
		if (!ctx.userWithRoles) {
			throw new TRPCError({ code: "UNAUTHORIZED" });
		}

		const userRoles = ctx.userWithRoles.roles.map((role) => role.name);
		const hasRequiredRole = requiredRoles.some((role) =>
			userRoles.includes(role),
		);

		if (!hasRequiredRole) {
			throw new TRPCError({
				code: "FORBIDDEN",
				message: `Access denied. Required roles: ${requiredRoles.join(", ")}`,
			});
		}

		return next({
			ctx: {
				...ctx,
				user: ctx.userWithRoles,
				userRoles,
			},
		});
	});
};

/**
 * Student-only procedure
 */
export const studentProcedure = createRoleProcedure(["STUDENT"]);

/**
 * Teacher-only procedure
 */
export const teacherProcedure = createRoleProcedure(["TEACHER"]);

/**
 * Parent-only procedure
 */
export const parentProcedure = createRoleProcedure(["PARENT"]);

/**
 * Admin-only procedure
 */
export const adminProcedure = createRoleProcedure(["ADMIN"]);

/**
 * Teacher or Admin procedure
 */
export const teacherOrAdminProcedure = createRoleProcedure([
	"TEACHER",
	"ADMIN",
]);

/**
 * Parent or Teacher procedure (for communication)
 */
export const parentOrTeacherProcedure = createRoleProcedure([
	"PARENT",
	"TEACHER",
]);
