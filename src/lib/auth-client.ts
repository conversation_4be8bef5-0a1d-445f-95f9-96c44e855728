import { createAuthClient } from "better-auth/react";

// Get the current subdomain for cross-subdomain support
const getCurrentBaseURL = () => {
	if (typeof window === "undefined") {
		// Server-side: use environment variable
		return process.env.NODE_ENV === "production"
			? "https://learnfunda.ai"
			: "http://localhost:3000";
	}

	// Client-side: determine base URL based on current domain
	if (process.env.NODE_ENV === "production") {
		// In production, always use the main domain for auth API
		// This ensures cross-subdomain session sharing works properly
		return "https://learnfunda.ai";
	} else {
		// In development, use localhost
		return "http://localhost:3000";
	}
};

export const authClient = createAuthClient({
	baseURL: getCurrentBaseURL(),
	// Enable cross-subdomain cookies
	fetchOptions: {
		credentials: "include",
	},
});

export const { signIn, signUp, signOut, useSession, getSession } = authClient;
