import {
	boolean,
	integer,
	pgEnum,
	pgTable,
	text,
	timestamp,
	unique,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { users } from "./users";

// Enums
export const petTypeEnum = pgEnum("pet_type", [
	"CAT",
	"DOG",
	"TURTLE",
	"PANDA",
	"MOUSE",
	"RABBIT",
]);

export const personalityTraitEnum = pgEnum("personality_trait", [
	"FRIENDLY",
	"ENERGETIC",
	"CALM",
	"PLAYFUL",
	"WISE",
	"CURIOUS",
	"SUPPORTIVE",
	"ENCOURAGING",
]);

// Pets table - Available pet types
export const pets = pgTable("pets", {
	id: uuid("id").primaryKey().defaultRandom(),
	type: petTypeEnum("type").notNull().unique(),
	name: varchar("name", { length: 100 }).notNull(),
	description: text("description").notNull(),
	imageUrl: varchar("image_url", { length: 500 }).notNull(),
	avatarUrl: varchar("avatar_url", { length: 500 }).notNull(),
	personalityTraits: text("personality_traits").notNull(), // JSON array of personality traits
	helpMessages: text("help_messages").notNull(), // JSON array of contextual help messages
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User pets table - User pet selections
export const userPets = pgTable(
	"user_pets",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: text("user_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull(),
		petId: uuid("pet_id")
			.references(() => pets.id, { onDelete: "cascade" })
			.notNull(),
		customName: varchar("custom_name", { length: 100 }), // User-given name for their pet
		selectedAt: timestamp("selected_at").defaultNow().notNull(),
		isActive: boolean("is_active").default(true).notNull(),
		// Future AI integration fields
		interactionCount: integer("interaction_count").default(0).notNull(),
		lastInteractionAt: timestamp("last_interaction_at"),
		personalityData: text("personality_data"), // JSON for AI personality customization
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => ({
		userPetUnique: unique().on(table.userId), // One pet per user for MVP
	}),
);

// Zod schemas for validation
export const insertPetSchema = createInsertSchema(pets, {
	name: (schema) => schema.min(1).max(100),
	description: (schema) => schema.min(1),
	imageUrl: (schema) => schema.url(),
	avatarUrl: (schema) => schema.url(),
	personalityTraits: (schema) => schema.min(1),
	helpMessages: (schema) => schema.min(1),
});

export const selectPetSchema = createSelectSchema(pets);

export const insertUserPetSchema = createInsertSchema(userPets, {
	customName: (schema) => schema.min(1).max(100).optional(),
});

export const selectUserPetSchema = createSelectSchema(userPets);

// Types
export type Pet = typeof pets.$inferSelect;
export type NewPet = typeof pets.$inferInsert;
export type UserPet = typeof userPets.$inferSelect;
export type NewUserPet = typeof userPets.$inferInsert;
