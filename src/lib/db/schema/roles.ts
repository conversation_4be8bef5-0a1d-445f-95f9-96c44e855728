import {
	boolean,
	pgEnum,
	pgTable,
	text,
	timestamp,
	unique,
	uuid,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { users } from "./users";

// Enums
export const roleTypeEnum = pgEnum("role_type", [
	"STUDENT",
	"PARENT",
	"TEACHER",
	"ADMIN",
]);

export const relationshipTypeEnum = pgEnum("relationship_type", [
	"PARENT_CHILD",
	"TEACHER_STUDENT",
]);

// User roles table - Role definitions
export const userRoles = pgTable("user_roles", {
	id: uuid("id").primaryKey().defaultRandom(),
	name: roleTypeEnum("name").notNull().unique(),
	description: text("description"),
	permissions: text("permissions"), // JSON array of permissions
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User role assignments table - User-role mappings
export const userRoleAssignments = pgTable(
	"user_role_assignments",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: text("user_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull(),
		roleId: uuid("role_id")
			.references(() => userRoles.id, { onDelete: "cascade" })
			.notNull(),
		assignedAt: timestamp("assigned_at").defaultNow().notNull(),
		assignedBy: text("assigned_by").references(() => users.id),
		isActive: boolean("is_active").default(true).notNull(),
		expiresAt: timestamp("expires_at"), // Optional expiration for temporary roles
	},
	(table) => ({
		userRoleUnique: unique().on(table.userId, table.roleId),
	}),
);

// User relationships table - Parent-child, teacher-student relationships
export const userRelationships = pgTable(
	"user_relationships",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		primaryUserId: text("primary_user_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull(),
		relatedUserId: text("related_user_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull(),
		relationshipType: relationshipTypeEnum("relationship_type").notNull(),
		isActive: boolean("is_active").default(true).notNull(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
		createdBy: text("created_by").references(() => users.id),
		metadata: text("metadata"), // JSON for additional relationship data
	},
	(table) => ({
		relationshipUnique: unique().on(
			table.primaryUserId,
			table.relatedUserId,
			table.relationshipType,
		),
	}),
);

// Zod schemas for validation
export const insertUserRoleSchema = createInsertSchema(userRoles);
export const selectUserRoleSchema = createSelectSchema(userRoles);

export const insertUserRoleAssignmentSchema =
	createInsertSchema(userRoleAssignments);
export const selectUserRoleAssignmentSchema =
	createSelectSchema(userRoleAssignments);

export const insertUserRelationshipSchema =
	createInsertSchema(userRelationships);
export const selectUserRelationshipSchema =
	createSelectSchema(userRelationships);

// Types
export type UserRole = typeof userRoles.$inferSelect;
export type NewUserRole = typeof userRoles.$inferInsert;
export type UserRoleAssignment = typeof userRoleAssignments.$inferSelect;
export type NewUserRoleAssignment = typeof userRoleAssignments.$inferInsert;
export type UserRelationship = typeof userRelationships.$inferSelect;
export type NewUserRelationship = typeof userRelationships.$inferInsert;
