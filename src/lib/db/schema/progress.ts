import {
	boolean,
	decimal,
	integer,
	pgEnum,
	pgTable,
	text,
	timestamp,
	unique,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { quizzes, topics, videos } from "./content";
import { users } from "./users";

// Enums
export const progressStatusEnum = pgEnum("progress_status", [
	"NOT_STARTED",
	"IN_PROGRESS",
	"COMPLETED",
]);

export const noteTypeEnum = pgEnum("note_type", [
	"GENERAL",
	"VIDEO_TIMESTAMP",
	"QUIZ_REVIEW",
]);

// User topic progress table - Learning progress tracking
export const userTopicProgress = pgTable(
	"user_topic_progress",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: text("user_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull(),
		topicId: uuid("topic_id")
			.references(() => topics.id, { onDelete: "cascade" })
			.notNull(),
		status: progressStatusEnum("status").default("NOT_STARTED").notNull(),
		progressPercentage: decimal("progress_percentage", {
			precision: 5,
			scale: 2,
		})
			.default("0.00")
			.notNull(),
		startedAt: timestamp("started_at"),
		completedAt: timestamp("completed_at"),
		lastAccessedAt: timestamp("last_accessed_at").defaultNow().notNull(),
		timeSpent: integer("time_spent").default(0).notNull(), // in seconds
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => ({
		userTopicUnique: unique().on(table.userId, table.topicId),
	}),
);

// User video progress table - Video watch progress
export const userVideoProgress = pgTable(
	"user_video_progress",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: text("user_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull(),
		videoId: uuid("video_id")
			.references(() => videos.id, { onDelete: "cascade" })
			.notNull(),
		watchedDuration: integer("watched_duration").default(0).notNull(), // in seconds
		totalDuration: integer("total_duration").notNull(), // in seconds
		progressPercentage: decimal("progress_percentage", {
			precision: 5,
			scale: 2,
		})
			.default("0.00")
			.notNull(),
		isCompleted: boolean("is_completed").default(false).notNull(),
		lastWatchedAt: timestamp("last_watched_at").defaultNow().notNull(),
		watchCount: integer("watch_count").default(1).notNull(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => ({
		userVideoUnique: unique().on(table.userId, table.videoId),
	}),
);

// User quiz attempts table - Quiz performance
export const userQuizAttempts = pgTable("user_quiz_attempts", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.references(() => users.id, { onDelete: "cascade" })
		.notNull(),
	quizId: uuid("quiz_id")
		.references(() => quizzes.id, { onDelete: "cascade" })
		.notNull(),
	attemptNumber: integer("attempt_number").notNull(),
	score: decimal("score", { precision: 5, scale: 2 }).notNull(),
	maxScore: decimal("max_score", { precision: 5, scale: 2 }).notNull(),
	percentage: decimal("percentage", { precision: 5, scale: 2 }).notNull(),
	timeSpent: integer("time_spent").notNull(), // in seconds
	answers: text("answers").notNull(), // JSON object with question_id: answer pairs
	isPassed: boolean("is_passed").notNull(),
	startedAt: timestamp("started_at").notNull(),
	completedAt: timestamp("completed_at").notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

// User notes table - Student notes
export const userNotes = pgTable("user_notes", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.references(() => users.id, { onDelete: "cascade" })
		.notNull(),
	topicId: uuid("topic_id")
		.references(() => topics.id, { onDelete: "cascade" })
		.notNull(),
	videoId: uuid("video_id").references(() => videos.id, {
		onDelete: "cascade",
	}), // Optional, for video-specific notes
	noteType: noteTypeEnum("note_type").default("GENERAL").notNull(),
	title: varchar("title", { length: 255 }),
	content: text("content").notNull(),
	videoTimestamp: integer("video_timestamp"), // in seconds, for video notes
	tags: text("tags"), // JSON array of tags
	isPrivate: boolean("is_private").default(true).notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Zod schemas for validation
export const insertUserTopicProgressSchema = createInsertSchema(
	userTopicProgress,
	{
		progressPercentage: (schema) => schema.min(0).max(100),
		timeSpent: (schema) => schema.min(0),
	},
);

export const selectUserTopicProgressSchema =
	createSelectSchema(userTopicProgress);

export const insertUserVideoProgressSchema = createInsertSchema(
	userVideoProgress,
	{
		watchedDuration: (schema) => schema.min(0),
		totalDuration: (schema) => schema.positive(),
		progressPercentage: (schema) => schema.min(0).max(100),
		watchCount: (schema) => schema.positive(),
	},
);

export const selectUserVideoProgressSchema =
	createSelectSchema(userVideoProgress);

export const insertUserQuizAttemptSchema = createInsertSchema(userQuizAttempts);

export const selectUserQuizAttemptSchema = createSelectSchema(userQuizAttempts);

export const insertUserNoteSchema = createInsertSchema(userNotes, {
	content: (schema) => schema.min(1),
	title: (schema) => schema.max(255).optional(),
	videoTimestamp: (schema) => schema.min(0).optional(),
});

export const selectUserNoteSchema = createSelectSchema(userNotes);

// Types
export type UserTopicProgress = typeof userTopicProgress.$inferSelect;
export type NewUserTopicProgress = typeof userTopicProgress.$inferInsert;
export type UserVideoProgress = typeof userVideoProgress.$inferSelect;
export type NewUserVideoProgress = typeof userVideoProgress.$inferInsert;
export type UserQuizAttempt = typeof userQuizAttempts.$inferSelect;
export type NewUserQuizAttempt = typeof userQuizAttempts.$inferInsert;
export type UserNote = typeof userNotes.$inferSelect;
export type NewUserNote = typeof userNotes.$inferInsert;
