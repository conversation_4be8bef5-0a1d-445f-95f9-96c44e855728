import {
	boolean,
	integer,
	pgEnum,
	pgTable,
	text,
	timestamp,
	unique,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { users } from "./users";

// Enums
export const streakTypeEnum = pgEnum("streak_type", [
	"DAILY_LOGIN",
	"VIDEO_COMPLETION",
	"QUIZ_COMPLETION",
	"NOTE_CREATION",
]);

export const badgeTypeEnum = pgEnum("badge_type", [
	"ACHIEVEMENT",
	"MILESTONE",
	"SPECIAL_EVENT",
	"STREAK",
]);

// User streaks table - Streak tracking by type
export const userStreaks = pgTable(
	"user_streaks",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: text("user_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull(),
		streakType: streakTypeEnum("streak_type").notNull(),
		currentStreak: integer("current_streak").default(0).notNull(),
		longestStreak: integer("longest_streak").default(0).notNull(),
		lastActivityDate: timestamp("last_activity_date"),
		isActive: boolean("is_active").default(true).notNull(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => ({
		userStreakTypeUnique: unique().on(table.userId, table.streakType),
	}),
);

// Badges table - Available badge definitions
export const badges = pgTable("badges", {
	id: uuid("id").primaryKey().defaultRandom(),
	name: varchar("name", { length: 255 }).notNull().unique(),
	description: text("description").notNull(),
	badgeType: badgeTypeEnum("badge_type").notNull(),
	iconUrl: varchar("icon_url", { length: 500 }).notNull(),
	criteria: text("criteria").notNull(), // JSON object defining earning criteria
	xpReward: integer("xp_reward").default(0).notNull(),
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User badges table - Earned badges
export const userBadges = pgTable(
	"user_badges",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: text("user_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull(),
		badgeId: uuid("badge_id")
			.references(() => badges.id, { onDelete: "cascade" })
			.notNull(),
		earnedAt: timestamp("earned_at").defaultNow().notNull(),
		progress: text("progress"), // JSON object for tracking progress toward badge
		createdAt: timestamp("created_at").defaultNow().notNull(),
	},
	(table) => ({
		userBadgeUnique: unique().on(table.userId, table.badgeId),
	}),
);

// User XP table - XP tracking and sources
export const userXp = pgTable("user_xp", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: text("user_id")
		.references(() => users.id, { onDelete: "cascade" })
		.notNull(),
	totalXp: integer("total_xp").default(0).notNull(),
	currentLevel: integer("current_level").default(1).notNull(),
	xpToNextLevel: integer("xp_to_next_level").default(100).notNull(),
	lastXpEarned: integer("last_xp_earned").default(0).notNull(),
	lastXpSource: varchar("last_xp_source", { length: 100 }),
	lastXpEarnedAt: timestamp("last_xp_earned_at"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Zod schemas for validation
export const insertUserStreakSchema = createInsertSchema(userStreaks, {
	currentStreak: (schema) => schema.min(0),
	longestStreak: (schema) => schema.min(0),
});

export const selectUserStreakSchema = createSelectSchema(userStreaks);

export const insertBadgeSchema = createInsertSchema(badges, {
	name: (schema) => schema.min(1).max(255),
	description: (schema) => schema.min(1),
	iconUrl: (schema) => schema.url(),
	criteria: (schema) => schema.min(1),
	xpReward: (schema) => schema.min(0),
});

export const selectBadgeSchema = createSelectSchema(badges);

export const insertUserBadgeSchema = createInsertSchema(userBadges);
export const selectUserBadgeSchema = createSelectSchema(userBadges);

export const insertUserXpSchema = createInsertSchema(userXp, {
	totalXp: (schema) => schema.min(0),
	currentLevel: (schema) => schema.min(1),
	xpToNextLevel: (schema) => schema.min(0),
	lastXpEarned: (schema) => schema.min(0),
});

export const selectUserXpSchema = createSelectSchema(userXp);

// Types
export type UserStreak = typeof userStreaks.$inferSelect;
export type NewUserStreak = typeof userStreaks.$inferInsert;
export type Badge = typeof badges.$inferSelect;
export type NewBadge = typeof badges.$inferInsert;
export type UserBadge = typeof userBadges.$inferSelect;
export type NewUserBadge = typeof userBadges.$inferInsert;
export type UserXp = typeof userXp.$inferSelect;
export type NewUserXp = typeof userXp.$inferInsert;
