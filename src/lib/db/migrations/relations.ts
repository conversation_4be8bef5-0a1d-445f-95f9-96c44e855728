import { relations } from "drizzle-orm/relations";
import {
	accounts,
	badges,
	pets,
	quizQuestions,
	quizzes,
	sessions,
	subjects,
	topics,
	userBadges,
	userNotes,
	userPets,
	userProfiles,
	userQuizAttempts,
	userRelationships,
	userRoleAssignments,
	userRoles,
	userStreaks,
	users,
	userTopicProgress,
	userVideoProgress,
	userXp,
	videos,
} from "./schema";

export const sessionsRelations = relations(sessions, ({ one }) => ({
	user: one(users, {
		fields: [sessions.userId],
		references: [users.id],
	}),
}));

export const usersRelations = relations(users, ({ many }) => ({
	sessions: many(sessions),
	accounts: many(accounts),
	userBadges: many(userBadges),
	userStreaks: many(userStreaks),
	userXps: many(userXp),
	userQuizAttempts: many(userQuizAttempts),
	userTopicProgresses: many(userTopicProgress),
	userVideoProgresses: many(userVideoProgress),
	userRelationships_primaryUserId: many(userRelationships, {
		relationName: "userRelationships_primaryUserId_users_id",
	}),
	userRelationships_relatedUserId: many(userRelationships, {
		relationName: "userRelationships_relatedUserId_users_id",
	}),
	userRelationships_createdBy: many(userRelationships, {
		relationName: "userRelationships_createdBy_users_id",
	}),
	userPets: many(userPets),
	userNotes: many(userNotes),
	userRoleAssignments_userId: many(userRoleAssignments, {
		relationName: "userRoleAssignments_userId_users_id",
	}),
	userRoleAssignments_assignedBy: many(userRoleAssignments, {
		relationName: "userRoleAssignments_assignedBy_users_id",
	}),
	userProfiles: many(userProfiles),
}));

export const quizQuestionsRelations = relations(quizQuestions, ({ one }) => ({
	quiz: one(quizzes, {
		fields: [quizQuestions.quizId],
		references: [quizzes.id],
	}),
}));

export const quizzesRelations = relations(quizzes, ({ one, many }) => ({
	quizQuestions: many(quizQuestions),
	topic: one(topics, {
		fields: [quizzes.topicId],
		references: [topics.id],
	}),
	userQuizAttempts: many(userQuizAttempts),
}));

export const accountsRelations = relations(accounts, ({ one }) => ({
	user: one(users, {
		fields: [accounts.userId],
		references: [users.id],
	}),
}));

export const topicsRelations = relations(topics, ({ one, many }) => ({
	quizzes: many(quizzes),
	videos: many(videos),
	subject: one(subjects, {
		fields: [topics.subjectId],
		references: [subjects.id],
	}),
	userTopicProgresses: many(userTopicProgress),
	userNotes: many(userNotes),
}));

export const videosRelations = relations(videos, ({ one, many }) => ({
	topic: one(topics, {
		fields: [videos.topicId],
		references: [topics.id],
	}),
	userVideoProgresses: many(userVideoProgress),
	userNotes: many(userNotes),
}));

export const userBadgesRelations = relations(userBadges, ({ one }) => ({
	user: one(users, {
		fields: [userBadges.userId],
		references: [users.id],
	}),
	badge: one(badges, {
		fields: [userBadges.badgeId],
		references: [badges.id],
	}),
}));

export const badgesRelations = relations(badges, ({ many }) => ({
	userBadges: many(userBadges),
}));

export const userStreaksRelations = relations(userStreaks, ({ one }) => ({
	user: one(users, {
		fields: [userStreaks.userId],
		references: [users.id],
	}),
}));

export const userXpRelations = relations(userXp, ({ one }) => ({
	user: one(users, {
		fields: [userXp.userId],
		references: [users.id],
	}),
}));

export const subjectsRelations = relations(subjects, ({ many }) => ({
	topics: many(topics),
}));

export const userQuizAttemptsRelations = relations(
	userQuizAttempts,
	({ one }) => ({
		user: one(users, {
			fields: [userQuizAttempts.userId],
			references: [users.id],
		}),
		quiz: one(quizzes, {
			fields: [userQuizAttempts.quizId],
			references: [quizzes.id],
		}),
	}),
);

export const userTopicProgressRelations = relations(
	userTopicProgress,
	({ one }) => ({
		user: one(users, {
			fields: [userTopicProgress.userId],
			references: [users.id],
		}),
		topic: one(topics, {
			fields: [userTopicProgress.topicId],
			references: [topics.id],
		}),
	}),
);

export const userVideoProgressRelations = relations(
	userVideoProgress,
	({ one }) => ({
		user: one(users, {
			fields: [userVideoProgress.userId],
			references: [users.id],
		}),
		video: one(videos, {
			fields: [userVideoProgress.videoId],
			references: [videos.id],
		}),
	}),
);

export const userRelationshipsRelations = relations(
	userRelationships,
	({ one }) => ({
		user_primaryUserId: one(users, {
			fields: [userRelationships.primaryUserId],
			references: [users.id],
			relationName: "userRelationships_primaryUserId_users_id",
		}),
		user_relatedUserId: one(users, {
			fields: [userRelationships.relatedUserId],
			references: [users.id],
			relationName: "userRelationships_relatedUserId_users_id",
		}),
		user_createdBy: one(users, {
			fields: [userRelationships.createdBy],
			references: [users.id],
			relationName: "userRelationships_createdBy_users_id",
		}),
	}),
);

export const userPetsRelations = relations(userPets, ({ one }) => ({
	user: one(users, {
		fields: [userPets.userId],
		references: [users.id],
	}),
	pet: one(pets, {
		fields: [userPets.petId],
		references: [pets.id],
	}),
}));

export const petsRelations = relations(pets, ({ many }) => ({
	userPets: many(userPets),
}));

export const userNotesRelations = relations(userNotes, ({ one }) => ({
	user: one(users, {
		fields: [userNotes.userId],
		references: [users.id],
	}),
	topic: one(topics, {
		fields: [userNotes.topicId],
		references: [topics.id],
	}),
	video: one(videos, {
		fields: [userNotes.videoId],
		references: [videos.id],
	}),
}));

export const userRoleAssignmentsRelations = relations(
	userRoleAssignments,
	({ one }) => ({
		user_userId: one(users, {
			fields: [userRoleAssignments.userId],
			references: [users.id],
			relationName: "userRoleAssignments_userId_users_id",
		}),
		userRole: one(userRoles, {
			fields: [userRoleAssignments.roleId],
			references: [userRoles.id],
		}),
		user_assignedBy: one(users, {
			fields: [userRoleAssignments.assignedBy],
			references: [users.id],
			relationName: "userRoleAssignments_assignedBy_users_id",
		}),
	}),
);

export const userRolesRelations = relations(userRoles, ({ many }) => ({
	userRoleAssignments: many(userRoleAssignments),
}));

export const userProfilesRelations = relations(userProfiles, ({ one }) => ({
	user: one(users, {
		fields: [userProfiles.userId],
		references: [users.id],
	}),
}));
