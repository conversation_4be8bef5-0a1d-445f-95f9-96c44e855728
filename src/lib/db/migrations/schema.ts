import {
	boolean,
	foreignKey,
	index,
	integer,
	numeric,
	pgEnum,
	pgTable,
	text,
	timestamp,
	unique,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";

export const badgeType = pgEnum("badge_type", [
	"ACHIEVEMENT",
	"MILESTONE",
	"SPECIAL_EVENT",
	"STREAK",
]);
export const contentStatus = pgEnum("content_status", [
	"DRAFT",
	"PUBLISHED",
	"ARCHIVED",
]);
export const difficultyLevel = pgEnum("difficulty_level", [
	"BEGINNER",
	"INTERMEDIATE",
	"ADVANCED",
]);
export const educationalBoard = pgEnum("educational_board", [
	"CBSE",
	"ICSE",
	"STATE_BOARD",
]);
export const noteType = pgEnum("note_type", [
	"GENERAL",
	"VIDEO_TIMESTAMP",
	"QUIZ_REVIEW",
]);
export const personalityTrait = pgEnum("personality_trait", [
	"FRIENDLY",
	"ENERGETIC",
	"CALM",
	"PLAYFUL",
	"WISE",
	"CURIOUS",
	"SUPPORTIVE",
	"ENCOURAGING",
]);
export const petType = pgEnum("pet_type", [
	"CAT",
	"DOG",
	"TURTLE",
	"PANDA",
	"MOUSE",
	"RABBIT",
]);
export const progressStatus = pgEnum("progress_status", [
	"NOT_STARTED",
	"IN_PROGRESS",
	"COMPLETED",
]);
export const questionType = pgEnum("question_type", [
	"MULTIPLE_CHOICE",
	"FILL_IN_BLANK",
	"DRAG_DROP",
	"TRUE_FALSE",
]);
export const relationshipType = pgEnum("relationship_type", [
	"PARENT_CHILD",
	"TEACHER_STUDENT",
]);
export const roleType = pgEnum("role_type", [
	"STUDENT",
	"PARENT",
	"TEACHER",
	"ADMIN",
]);
export const standard = pgEnum("standard", ["8th", "9th", "10th"]);
export const streakType = pgEnum("streak_type", [
	"DAILY_LOGIN",
	"VIDEO_COMPLETION",
	"QUIZ_COMPLETION",
	"NOTE_CREATION",
]);

export const sessions = pgTable(
	"sessions",
	{
		id: text().primaryKey().notNull(),
		expiresAt: timestamp("expires_at", { mode: "string" }).notNull(),
		token: text().notNull(),
		createdAt: timestamp("created_at", { mode: "string" }).notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" }).notNull(),
		ipAddress: text("ip_address"),
		userAgent: text("user_agent"),
		userId: text("user_id").notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "sessions_user_id_users_id_fk",
		}).onDelete("cascade"),
		unique("sessions_token_unique").on(table.token),
	],
);

export const quizQuestions = pgTable(
	"quiz_questions",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		quizId: uuid("quiz_id").notNull(),
		questionType: questionType("question_type").notNull(),
		questionText: text("question_text").notNull(),
		options: text(),
		correctAnswer: text("correct_answer").notNull(),
		explanation: text(),
		points: integer().default(1).notNull(),
		sortOrder: integer("sort_order").default(0).notNull(),
		isActive: boolean("is_active").default(true).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.quizId],
			foreignColumns: [quizzes.id],
			name: "quiz_questions_quiz_id_quizzes_id_fk",
		}).onDelete("cascade"),
	],
);

export const verifications = pgTable("verifications", {
	id: text().primaryKey().notNull(),
	identifier: text().notNull(),
	value: text().notNull(),
	expiresAt: timestamp("expires_at", { mode: "string" }).notNull(),
	createdAt: timestamp("created_at", { mode: "string" }),
	updatedAt: timestamp("updated_at", { mode: "string" }),
});

export const accounts = pgTable(
	"accounts",
	{
		id: text().primaryKey().notNull(),
		accountId: text("account_id").notNull(),
		providerId: text("provider_id").notNull(),
		userId: text("user_id").notNull(),
		accessToken: text("access_token"),
		refreshToken: text("refresh_token"),
		idToken: text("id_token"),
		accessTokenExpiresAt: timestamp("access_token_expires_at", {
			mode: "string",
		}),
		refreshTokenExpiresAt: timestamp("refresh_token_expires_at", {
			mode: "string",
		}),
		scope: text(),
		password: text(),
		createdAt: timestamp("created_at", { mode: "string" }).notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" }).notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "accounts_user_id_users_id_fk",
		}).onDelete("cascade"),
	],
);

export const quizzes = pgTable(
	"quizzes",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		topicId: uuid("topic_id").notNull(),
		title: varchar({ length: 255 }).notNull(),
		description: text(),
		instructions: text(),
		timeLimit: integer("time_limit"),
		passingScore: numeric("passing_score", { precision: 5, scale: 2 }),
		maxAttempts: integer("max_attempts"),
		isActive: boolean("is_active").default(true).notNull(),
		sortOrder: integer("sort_order").default(0).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.topicId],
			foreignColumns: [topics.id],
			name: "quizzes_topic_id_topics_id_fk",
		}).onDelete("cascade"),
	],
);

export const videos = pgTable(
	"videos",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		topicId: uuid("topic_id").notNull(),
		title: varchar({ length: 255 }).notNull(),
		description: text(),
		videoUrl: varchar("video_url", { length: 500 }).notNull(),
		thumbnailUrl: varchar("thumbnail_url", { length: 500 }),
		duration: integer().notNull(),
		transcriptUrl: varchar("transcript_url", { length: 500 }),
		status: contentStatus().default("DRAFT").notNull(),
		isActive: boolean("is_active").default(true).notNull(),
		sortOrder: integer("sort_order").default(0).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.topicId],
			foreignColumns: [topics.id],
			name: "videos_topic_id_topics_id_fk",
		}).onDelete("cascade"),
	],
);

export const subjects = pgTable("subjects", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	standard: standard().notNull(),
	educationalBoard: educationalBoard("educational_board").notNull(),
	iconUrl: varchar("icon_url", { length: 500 }),
	colorCode: varchar("color_code", { length: 7 }),
	isActive: boolean("is_active").default(true).notNull(),
	sortOrder: integer("sort_order").default(0).notNull(),
	createdAt: timestamp("created_at", { mode: "string" }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: "string" }).defaultNow().notNull(),
});

export const userBadges = pgTable(
	"user_badges",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		userId: text("user_id").notNull(),
		badgeId: uuid("badge_id").notNull(),
		earnedAt: timestamp("earned_at", { mode: "string" }).defaultNow().notNull(),
		progress: text(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "user_badges_user_id_users_id_fk",
		}).onDelete("cascade"),
		foreignKey({
			columns: [table.badgeId],
			foreignColumns: [badges.id],
			name: "user_badges_badge_id_badges_id_fk",
		}).onDelete("cascade"),
		unique("user_badges_user_id_badge_id_unique").on(
			table.userId,
			table.badgeId,
		),
	],
);

export const userStreaks = pgTable(
	"user_streaks",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		userId: text("user_id").notNull(),
		streakType: streakType("streak_type").notNull(),
		currentStreak: integer("current_streak").default(0).notNull(),
		longestStreak: integer("longest_streak").default(0).notNull(),
		lastActivityDate: timestamp("last_activity_date", { mode: "string" }),
		isActive: boolean("is_active").default(true).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "user_streaks_user_id_users_id_fk",
		}).onDelete("cascade"),
		unique("user_streaks_user_id_streak_type_unique").on(
			table.userId,
			table.streakType,
		),
	],
);

export const badges = pgTable(
	"badges",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		name: varchar({ length: 255 }).notNull(),
		description: text().notNull(),
		badgeType: badgeType("badge_type").notNull(),
		iconUrl: varchar("icon_url", { length: 500 }).notNull(),
		criteria: text().notNull(),
		xpReward: integer("xp_reward").default(0).notNull(),
		isActive: boolean("is_active").default(true).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [unique("badges_name_unique").on(table.name)],
);

export const userXp = pgTable(
	"user_xp",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		userId: text("user_id").notNull(),
		totalXp: integer("total_xp").default(0).notNull(),
		currentLevel: integer("current_level").default(1).notNull(),
		xpToNextLevel: integer("xp_to_next_level").default(100).notNull(),
		lastXpEarned: integer("last_xp_earned").default(0).notNull(),
		lastXpSource: varchar("last_xp_source", { length: 100 }),
		lastXpEarnedAt: timestamp("last_xp_earned_at", { mode: "string" }),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "user_xp_user_id_users_id_fk",
		}).onDelete("cascade"),
	],
);

export const topics = pgTable(
	"topics",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		subjectId: uuid("subject_id").notNull(),
		name: varchar({ length: 255 }).notNull(),
		description: text(),
		difficultyLevel: difficultyLevel("difficulty_level").notNull(),
		estimatedDuration: integer("estimated_duration"),
		prerequisites: text(),
		learningObjectives: text("learning_objectives"),
		isActive: boolean("is_active").default(true).notNull(),
		sortOrder: integer("sort_order").default(0).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.subjectId],
			foreignColumns: [subjects.id],
			name: "topics_subject_id_subjects_id_fk",
		}).onDelete("cascade"),
	],
);

export const pets = pgTable(
	"pets",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		type: petType().notNull(),
		name: varchar({ length: 100 }).notNull(),
		description: text().notNull(),
		imageUrl: varchar("image_url", { length: 500 }).notNull(),
		avatarUrl: varchar("avatar_url", { length: 500 }).notNull(),
		personalityTraits: text("personality_traits").notNull(),
		helpMessages: text("help_messages").notNull(),
		isActive: boolean("is_active").default(true).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [unique("pets_type_unique").on(table.type)],
);

export const userQuizAttempts = pgTable(
	"user_quiz_attempts",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		userId: text("user_id").notNull(),
		quizId: uuid("quiz_id").notNull(),
		attemptNumber: integer("attempt_number").notNull(),
		score: numeric({ precision: 5, scale: 2 }).notNull(),
		maxScore: numeric("max_score", { precision: 5, scale: 2 }).notNull(),
		percentage: numeric({ precision: 5, scale: 2 }).notNull(),
		timeSpent: integer("time_spent").notNull(),
		answers: text().notNull(),
		isPassed: boolean("is_passed").notNull(),
		startedAt: timestamp("started_at", { mode: "string" }).notNull(),
		completedAt: timestamp("completed_at", { mode: "string" }).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "user_quiz_attempts_user_id_users_id_fk",
		}).onDelete("cascade"),
		foreignKey({
			columns: [table.quizId],
			foreignColumns: [quizzes.id],
			name: "user_quiz_attempts_quiz_id_quizzes_id_fk",
		}).onDelete("cascade"),
	],
);

export const userTopicProgress = pgTable(
	"user_topic_progress",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		userId: text("user_id").notNull(),
		topicId: uuid("topic_id").notNull(),
		status: progressStatus().default("NOT_STARTED").notNull(),
		progressPercentage: numeric("progress_percentage", {
			precision: 5,
			scale: 2,
		})
			.default("0.00")
			.notNull(),
		startedAt: timestamp("started_at", { mode: "string" }),
		completedAt: timestamp("completed_at", { mode: "string" }),
		lastAccessedAt: timestamp("last_accessed_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		timeSpent: integer("time_spent").default(0).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "user_topic_progress_user_id_users_id_fk",
		}).onDelete("cascade"),
		foreignKey({
			columns: [table.topicId],
			foreignColumns: [topics.id],
			name: "user_topic_progress_topic_id_topics_id_fk",
		}).onDelete("cascade"),
		unique("user_topic_progress_user_id_topic_id_unique").on(
			table.userId,
			table.topicId,
		),
	],
);

export const userVideoProgress = pgTable(
	"user_video_progress",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		userId: text("user_id").notNull(),
		videoId: uuid("video_id").notNull(),
		watchedDuration: integer("watched_duration").default(0).notNull(),
		totalDuration: integer("total_duration").notNull(),
		progressPercentage: numeric("progress_percentage", {
			precision: 5,
			scale: 2,
		})
			.default("0.00")
			.notNull(),
		isCompleted: boolean("is_completed").default(false).notNull(),
		lastWatchedAt: timestamp("last_watched_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		watchCount: integer("watch_count").default(1).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "user_video_progress_user_id_users_id_fk",
		}).onDelete("cascade"),
		foreignKey({
			columns: [table.videoId],
			foreignColumns: [videos.id],
			name: "user_video_progress_video_id_videos_id_fk",
		}).onDelete("cascade"),
		unique("user_video_progress_user_id_video_id_unique").on(
			table.userId,
			table.videoId,
		),
	],
);

export const userRelationships = pgTable(
	"user_relationships",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		primaryUserId: text("primary_user_id").notNull(),
		relatedUserId: text("related_user_id").notNull(),
		relationshipType: relationshipType("relationship_type").notNull(),
		isActive: boolean("is_active").default(true).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		createdBy: text("created_by"),
		metadata: text(),
	},
	(table) => [
		foreignKey({
			columns: [table.primaryUserId],
			foreignColumns: [users.id],
			name: "user_relationships_primary_user_id_users_id_fk",
		}).onDelete("cascade"),
		foreignKey({
			columns: [table.relatedUserId],
			foreignColumns: [users.id],
			name: "user_relationships_related_user_id_users_id_fk",
		}).onDelete("cascade"),
		foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "user_relationships_created_by_users_id_fk",
		}),
		unique(
			"user_relationships_primary_user_id_related_user_id_relationship",
		).on(table.primaryUserId, table.relatedUserId, table.relationshipType),
	],
);

export const userPets = pgTable(
	"user_pets",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		userId: text("user_id").notNull(),
		petId: uuid("pet_id").notNull(),
		customName: varchar("custom_name", { length: 100 }),
		selectedAt: timestamp("selected_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		isActive: boolean("is_active").default(true).notNull(),
		interactionCount: integer("interaction_count").default(0).notNull(),
		lastInteractionAt: timestamp("last_interaction_at", { mode: "string" }),
		personalityData: text("personality_data"),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "user_pets_user_id_users_id_fk",
		}).onDelete("cascade"),
		foreignKey({
			columns: [table.petId],
			foreignColumns: [pets.id],
			name: "user_pets_pet_id_pets_id_fk",
		}).onDelete("cascade"),
		unique("user_pets_user_id_unique").on(table.userId),
	],
);

export const userNotes = pgTable(
	"user_notes",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		userId: text("user_id").notNull(),
		topicId: uuid("topic_id").notNull(),
		videoId: uuid("video_id"),
		noteType: noteType("note_type").default("GENERAL").notNull(),
		title: varchar({ length: 255 }),
		content: text().notNull(),
		videoTimestamp: integer("video_timestamp"),
		tags: text(),
		isPrivate: boolean("is_private").default(true).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [
		foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "user_notes_user_id_users_id_fk",
		}).onDelete("cascade"),
		foreignKey({
			columns: [table.topicId],
			foreignColumns: [topics.id],
			name: "user_notes_topic_id_topics_id_fk",
		}).onDelete("cascade"),
		foreignKey({
			columns: [table.videoId],
			foreignColumns: [videos.id],
			name: "user_notes_video_id_videos_id_fk",
		}).onDelete("cascade"),
	],
);

export const userRoles = pgTable(
	"user_roles",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		name: roleType().notNull(),
		description: text(),
		permissions: text(),
		isActive: boolean("is_active").default(true).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [unique("user_roles_name_unique").on(table.name)],
);

export const userRoleAssignments = pgTable(
	"user_role_assignments",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		userId: text("user_id").notNull(),
		roleId: uuid("role_id").notNull(),
		assignedAt: timestamp("assigned_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		assignedBy: text("assigned_by"),
		isActive: boolean("is_active").default(true).notNull(),
		expiresAt: timestamp("expires_at", { mode: "string" }),
	},
	(table) => [
		foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "user_role_assignments_user_id_users_id_fk",
		}).onDelete("cascade"),
		foreignKey({
			columns: [table.roleId],
			foreignColumns: [userRoles.id],
			name: "user_role_assignments_role_id_user_roles_id_fk",
		}).onDelete("cascade"),
		foreignKey({
			columns: [table.assignedBy],
			foreignColumns: [users.id],
			name: "user_role_assignments_assigned_by_users_id_fk",
		}),
		unique("user_role_assignments_user_id_role_id_unique").on(
			table.userId,
			table.roleId,
		),
	],
);

export const users = pgTable(
	"users",
	{
		id: text().primaryKey().notNull(),
		name: text().notNull(),
		email: text().notNull(),
		emailVerified: boolean("email_verified").notNull(),
		image: text(),
		createdAt: timestamp("created_at", { mode: "string" }).notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" }).notNull(),
		lastLoginAt: timestamp("last_login_at", { mode: "string" }),
		isActive: boolean("is_active").default(true).notNull(),
	},
	(table) => [
		index("users_email_idx").using(
			"btree",
			table.email.asc().nullsLast().op("text_ops"),
		),
		index("users_is_active_idx").using(
			"btree",
			table.isActive.asc().nullsLast().op("bool_ops"),
		),
		index("users_last_login_idx").using(
			"btree",
			table.lastLoginAt.asc().nullsLast().op("timestamp_ops"),
		),
		unique("users_email_unique").on(table.email),
	],
);

export const userProfiles = pgTable(
	"user_profiles",
	{
		id: uuid().defaultRandom().primaryKey().notNull(),
		userId: text("user_id").notNull(),
		fullName: varchar("full_name", { length: 255 }).notNull(),
		standard: standard().notNull(),
		schoolName: varchar("school_name", { length: 255 }).notNull(),
		educationalBoard: educationalBoard("educational_board").notNull(),
		interests: text(),
		dislikes: text(),
		profileCompleted: boolean("profile_completed").default(false).notNull(),
		createdAt: timestamp("created_at", { mode: "string" })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { mode: "string" })
			.defaultNow()
			.notNull(),
	},
	(table) => [
		index("user_profiles_completed_idx").using(
			"btree",
			table.profileCompleted.asc().nullsLast().op("bool_ops"),
		),
		index("user_profiles_standard_board_idx").using(
			"btree",
			table.standard.asc().nullsLast().op("enum_ops"),
			table.educationalBoard.asc().nullsLast().op("enum_ops"),
		),
		index("user_profiles_user_id_idx").using(
			"btree",
			table.userId.asc().nullsLast().op("text_ops"),
		),
		foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "user_profiles_user_id_users_id_fk",
		}).onDelete("cascade"),
		unique("user_profiles_user_id_unique").on(table.userId),
	],
);
