import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { subjects } from "../schema/content";

// Direct database connection for scripts
const DATABASE_URL = process.env.DATABASE_URL;
if (!DATABASE_URL) {
	throw new Error("DATABASE_URL environment variable is required");
}
const client = postgres(DATABASE_URL, { prepare: false });
const db = drizzle(client);

/**
 * Seeds the database with predefined subject entries for 8th, 9th, and 10th standards under the CBSE board.
 *
 * Inserts subject metadata such as name, description, standard, educational board, icon URL, color code, and sort order into the `subjects` table. Existing subjects are not duplicated.
 */
export async function seedSubjects() {
	const subjectData = [
		// 8th Standard Subjects
		{
			name: "Mathematics",
			description:
				"Fundamental mathematical concepts including algebra, geometry, and arithmetic",
			standard: "8th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/math.svg",
			colorCode: "#3B82F6",
			sortOrder: 1,
		},
		{
			name: "Science",
			description: "Basic concepts in physics, chemistry, and biology",
			standard: "8th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/science.svg",
			colorCode: "#10B981",
			sortOrder: 2,
		},
		{
			name: "English",
			description: "English language, literature, and communication skills",
			standard: "8th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/english.svg",
			colorCode: "#8B5CF6",
			sortOrder: 3,
		},
		{
			name: "Hindi",
			description: "Hindi language and literature",
			standard: "8th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/hindi.svg",
			colorCode: "#F59E0B",
			sortOrder: 4,
		},
		{
			name: "Social Studies",
			description: "History, geography, civics, and economics",
			standard: "8th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/social-studies.svg",
			colorCode: "#EF4444",
			sortOrder: 5,
		},
		{
			name: "Computer Science",
			description: "Basic programming and computer literacy",
			standard: "8th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/computer.svg",
			colorCode: "#6366F1",
			sortOrder: 6,
		},

		// 9th Standard Subjects
		{
			name: "Mathematics",
			description:
				"Advanced algebra, geometry, and introduction to trigonometry",
			standard: "9th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/math.svg",
			colorCode: "#3B82F6",
			sortOrder: 1,
		},
		{
			name: "Physics",
			description: "Fundamental concepts of motion, force, energy, and matter",
			standard: "9th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/physics.svg",
			colorCode: "#06B6D4",
			sortOrder: 2,
		},
		{
			name: "Chemistry",
			description: "Atoms, molecules, chemical reactions, and periodic table",
			standard: "9th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/chemistry.svg",
			colorCode: "#84CC16",
			sortOrder: 3,
		},
		{
			name: "Biology",
			description:
				"Cell structure, life processes, and diversity of living organisms",
			standard: "9th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/biology.svg",
			colorCode: "#22C55E",
			sortOrder: 4,
		},
		{
			name: "English",
			description: "Advanced English language and literature",
			standard: "9th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/english.svg",
			colorCode: "#8B5CF6",
			sortOrder: 5,
		},
		{
			name: "Hindi",
			description: "Advanced Hindi language and literature",
			standard: "9th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/hindi.svg",
			colorCode: "#F59E0B",
			sortOrder: 6,
		},
		{
			name: "Social Studies",
			description: "Advanced history, geography, civics, and economics",
			standard: "9th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/social-studies.svg",
			colorCode: "#EF4444",
			sortOrder: 7,
		},
		{
			name: "Computer Science",
			description: "Programming fundamentals and computational thinking",
			standard: "9th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/computer.svg",
			colorCode: "#6366F1",
			sortOrder: 8,
		},

		// 10th Standard Subjects (similar structure)
		{
			name: "Mathematics",
			description:
				"Advanced mathematics including coordinate geometry and trigonometry",
			standard: "10th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/math.svg",
			colorCode: "#3B82F6",
			sortOrder: 1,
		},
		{
			name: "Physics",
			description:
				"Light, electricity, magnetic effects, and natural phenomena",
			standard: "10th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/physics.svg",
			colorCode: "#06B6D4",
			sortOrder: 2,
		},
		{
			name: "Chemistry",
			description:
				"Acids, bases, metals, carbon compounds, and chemical reactions",
			standard: "10th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/chemistry.svg",
			colorCode: "#84CC16",
			sortOrder: 3,
		},
		{
			name: "Biology",
			description:
				"Life processes, heredity, evolution, and natural resource management",
			standard: "10th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/biology.svg",
			colorCode: "#22C55E",
			sortOrder: 4,
		},
		{
			name: "English",
			description: "Board exam preparation with advanced language skills",
			standard: "10th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/english.svg",
			colorCode: "#8B5CF6",
			sortOrder: 5,
		},
		{
			name: "Hindi",
			description: "Board exam preparation with advanced Hindi skills",
			standard: "10th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/hindi.svg",
			colorCode: "#F59E0B",
			sortOrder: 6,
		},
		{
			name: "Social Studies",
			description: "Board exam preparation covering all social science topics",
			standard: "10th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/social-studies.svg",
			colorCode: "#EF4444",
			sortOrder: 7,
		},
		{
			name: "Computer Science",
			description: "Advanced programming and computer applications",
			standard: "10th" as const,
			educationalBoard: "CBSE" as const,
			iconUrl: "/images/subjects/computer.svg",
			colorCode: "#6366F1",
			sortOrder: 8,
		},
	];

	for (const subject of subjectData) {
		await db.insert(subjects).values(subject).onConflictDoNothing();
	}
}
