// Re-export all Zod validation schemas for easy access

export {
	type Account,
	insertAccountSchema,
	insertSessionSchema,
	insertVerificationSchema,
	type NewAccount,
	type NewSession,
	type NewVerification,
	type Session,
	selectAccountSchema,
	selectSessionSchema,
	selectVerificationSchema,
	type Verification,
} from "./schema/auth";
export {
	insertQuizQuestionSchema,
	insertQuizSchema,
	insertSubjectSchema,
	insertTopicSchema,
	insertVideoSchema,
	type NewQuiz,
	type NewQuizQuestion,
	type NewSubject,
	type NewTopic,
	type NewVideo,
	type Quiz,
	type QuizQuestion,
	type Subject,
	selectQuizQuestionSchema,
	selectQuizSchema,
	selectSubjectSchema,
	selectTopicSchema,
	selectVideoSchema,
	type Topic,
	type Video,
} from "./schema/content";
export {
	type Badge,
	insertBadgeSchema,
	insertUserBadgeSchema,
	insertUserStreakSchema,
	insertUserXpSchema,
	type NewBadge,
	type NewUserBadge,
	type NewUserStreak,
	type NewUserXp,
	selectBadgeSchema,
	selectUserBadgeSchema,
	selectUserStreakSchema,
	selectUserXpSchema,
	type UserBadge,
	type UserStreak,
	type UserXp,
} from "./schema/gamification";
export {
	insertPetSchema,
	insertUserPetSchema,
	type NewPet,
	type NewUserPet,
	type Pet,
	selectPetSchema,
	selectUserPetSchema,
	type UserPet,
} from "./schema/pets";

export {
	insertUserNoteSchema,
	insertUserQuizAttemptSchema,
	insertUserTopicProgressSchema,
	insertUserVideoProgressSchema,
	type NewUserNote,
	type NewUserQuizAttempt,
	type NewUserTopicProgress,
	type NewUserVideoProgress,
	selectUserNoteSchema,
	selectUserQuizAttemptSchema,
	selectUserTopicProgressSchema,
	selectUserVideoProgressSchema,
	type UserNote,
	type UserQuizAttempt,
	type UserTopicProgress,
	type UserVideoProgress,
} from "./schema/progress";
export {
	insertUserRelationshipSchema,
	insertUserRoleAssignmentSchema,
	insertUserRoleSchema,
	type NewUserRelationship,
	type NewUserRole,
	type NewUserRoleAssignment,
	selectUserRelationshipSchema,
	selectUserRoleAssignmentSchema,
	selectUserRoleSchema,
	type UserRelationship,
	type UserRole,
	type UserRoleAssignment,
} from "./schema/roles";
export {
	insertUserProfileSchema,
	insertUserSchema,
	type NewUser,
	type NewUserProfile,
	selectUserProfileSchema,
	selectUserSchema,
	type User,
	type UserProfile,
} from "./schema/users";
