import type { Logger } from "drizzle-orm";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { env } from "@/env";

class MyLogger implements Logger {
	logQuery(query: string, params: unknown[]): void {
		console.log({ query, params });
	}
}

// Disable prefetch as it is not supported for "Transaction" pool mode
const client = postgres(env.DATABASE_URL, { prepare: false });
export const db = drizzle(client, { logger: new MyLogger() });

export type Database = typeof db;
