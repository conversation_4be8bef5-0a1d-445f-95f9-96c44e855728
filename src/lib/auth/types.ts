import type { roleTypeEnum } from "@/lib/db/schema/roles";

// Role types based on database enum
export type RoleType = (typeof roleTypeEnum.enumValues)[number];

// Subdomain mapping type
export type SubdomainType = "students" | "teachers" | "admins" | "parents";

// User with roles type
export interface UserWithRoles {
	id: string;
	name: string;
	email: string;
	emailVerified: boolean;
	image?: string | null;
	roles: Array<{
		id: string;
		name: RoleType;
		description?: string | null;
		permissions?: string | null;
		isActive: boolean;
		assignedAt: Date;
		expiresAt?: Date | null;
	}>;
}

// Role assignment type
export interface RoleAssignment {
	id: string;
	userId: string;
	roleId: string;
	assignedAt: Date;
	assignedBy?: string | null;
	isActive: boolean;
	expiresAt?: Date | null;
}

// Permission type
export type Permission =
	| "read:own_profile"
	| "update:own_profile"
	| "read:content"
	| "create:notes"
	| "read:own_progress"
	| "update:own_progress"
	| "take:quizzes"
	| "watch:videos"
	| "read:children_progress"
	| "read:children_profile"
	| "communicate:teachers"
	| "read:student_progress"
	| "read:student_profile"
	| "create:content"
	| "update:content"
	| "grade:assignments"
	| "communicate:parents"
	| "read:all"
	| "create:all"
	| "update:all"
	| "delete:all"
	| "manage:users"
	| "manage:roles"
	| "manage:system";

// Role permissions mapping
export interface RolePermissions {
	STUDENT: Permission[];
	PARENT: Permission[];
	TEACHER: Permission[];
	ADMIN: Permission[];
}

// Session with roles
export interface SessionWithRoles {
	user: UserWithRoles;
	session: {
		id: string;
		expiresAt: Date;
		token: string;
		createdAt: Date;
		updatedAt: Date;
		ipAddress?: string | null;
		userAgent?: string | null;
		userId: string;
	};
}
