import { and, eq } from "drizzle-orm";
import { db } from "@/lib/db";
import { userRoleAssignments, userRoles } from "@/lib/db/schema/roles";
import { users } from "@/lib/db/schema/users";
import type {
	Permission,
	RolePermissions,
	RoleType,
	SubdomainType,
	UserWithRoles,
} from "./types";

// Role to subdomain mapping
export const ROLE_SUBDOMAIN_MAP: Record<RoleType, SubdomainType> = {
	STUDENT: "students",
	PARENT: "parents",
	TEACHER: "teachers",
	ADMIN: "admins",
} as const;

// Subdomain to role mapping (reverse lookup)
export const SUBDOMAIN_ROLE_MAP: Record<SubdomainType, RoleType> = {
	students: "STUDENT",
	parents: "PARENT",
	teachers: "TEACHER",
	admins: "ADMIN",
} as const;

// Role permissions mapping
export const ROLE_PERMISSIONS: RolePermissions = {
	STUDENT: [
		"read:own_profile",
		"update:own_profile",
		"read:content",
		"create:notes",
		"read:own_progress",
		"update:own_progress",
		"take:quizzes",
		"watch:videos",
	],
	PARENT: [
		"read:own_profile",
		"update:own_profile",
		"read:children_progress",
		"read:children_profile",
		"communicate:teachers",
	],
	TEACHER: [
		"read:own_profile",
		"update:own_profile",
		"read:student_progress",
		"read:student_profile",
		"create:content",
		"update:content",
		"grade:assignments",
		"communicate:parents",
	],
	ADMIN: [
		"read:all",
		"create:all",
		"update:all",
		"delete:all",
		"manage:users",
		"manage:roles",
		"manage:system",
	],
} as const;

/**
 * Get user with their roles from the database
 */
export async function getUserWithRoles(
	userId: string,
): Promise<UserWithRoles | null> {
	try {
		const result = await db
			.select({
				user: users,
				role: userRoles,
				assignment: userRoleAssignments,
			})
			.from(users)
			.leftJoin(userRoleAssignments, eq(userRoleAssignments.userId, users.id))
			.leftJoin(userRoles, eq(userRoles.id, userRoleAssignments.roleId))
			.where(
				and(
					eq(users.id, userId),
					eq(userRoleAssignments.isActive, true),
					eq(userRoles.isActive, true),
				),
			);

		if (!result.length || !result[0]?.user) {
			return null;
		}

		const user = result[0].user;
		const roles = result
			.filter(
				(
					r,
				): r is typeof r & {
					role: NonNullable<typeof r.role>;
					assignment: NonNullable<typeof r.assignment>;
				} => r.role !== null && r.assignment !== null,
			)
			.map((r) => ({
				id: r.role.id,
				name: r.role.name,
				description: r.role.description,
				permissions: r.role.permissions,
				isActive: r.assignment.isActive,
				assignedAt: r.assignment.assignedAt,
				expiresAt: r.assignment.expiresAt,
			}));

		return {
			...user,
			roles,
		};
	} catch (error) {
		console.error("Error fetching user with roles:", error);
		return null;
	}
}

/**
 * Check if user has a specific role
 */
export async function hasRole(
	userId: string,
	roleName: RoleType,
): Promise<boolean> {
	try {
		const userWithRoles = await getUserWithRoles(userId);
		return userWithRoles?.roles.some((role) => role.name === roleName) ?? false;
	} catch (error) {
		console.error("Error checking user role:", error);
		return false;
	}
}

/**
 * Check if user has any of the specified roles
 */
export async function hasAnyRole(
	userId: string,
	roleNames: RoleType[],
): Promise<boolean> {
	try {
		const userWithRoles = await getUserWithRoles(userId);
		return (
			userWithRoles?.roles.some((role) => roleNames.includes(role.name)) ??
			false
		);
	} catch (error) {
		console.error("Error checking user roles:", error);
		return false;
	}
}

/**
 * Get user's primary role (first active role)
 */
export async function getUserPrimaryRole(
	userId: string,
): Promise<RoleType | null> {
	try {
		const userWithRoles = await getUserWithRoles(userId);
		return userWithRoles?.roles[0]?.name ?? null;
	} catch (error) {
		console.error("Error getting user primary role:", error);
		return null;
	}
}

/**
 * Get subdomain for a specific role
 */
export function getSubdomainForRole(role: RoleType): SubdomainType {
	return ROLE_SUBDOMAIN_MAP[role];
}

/**
 * Get role for a specific subdomain
 */
export function getRoleForSubdomain(subdomain: SubdomainType): RoleType {
	return SUBDOMAIN_ROLE_MAP[subdomain];
}

/**
 * Get user's appropriate subdomain based on their primary role
 */
export async function getUserSubdomain(
	userId: string,
): Promise<SubdomainType | null> {
	try {
		const primaryRole = await getUserPrimaryRole(userId);
		return primaryRole ? getSubdomainForRole(primaryRole) : null;
	} catch (error) {
		console.error("Error getting user subdomain:", error);
		return null;
	}
}

/**
 * Check if user has permission
 */
export async function hasPermission(
	userId: string,
	permission: Permission,
): Promise<boolean> {
	try {
		const userWithRoles = await getUserWithRoles(userId);
		if (!userWithRoles) return false;

		return userWithRoles.roles.some((role) => {
			const rolePermissions = ROLE_PERMISSIONS[role.name];
			return rolePermissions.includes(permission);
		});
	} catch (error) {
		console.error("Error checking user permission:", error);
		return false;
	}
}

/**
 * Assign role to user
 */
export async function assignRoleToUser(
	userId: string,
	roleName: RoleType,
	assignedBy?: string,
): Promise<boolean> {
	try {
		console.log(
			`assignRoleToUser: Looking for role ${roleName} for user ${userId}`,
		);

		// Get the role ID
		const role = await db
			.select()
			.from(userRoles)
			.where(and(eq(userRoles.name, roleName), eq(userRoles.isActive, true)))
			.limit(1);

		if (!role.length) {
			console.error(`Role ${roleName} not found in database`);
			// Let's also check what roles exist
			const allRoles = await db.select().from(userRoles);
			console.log(
				"Available roles:",
				allRoles.map((r) => r.name),
			);
			return false;
		}

		console.log(`Found role ${roleName} with ID: ${role[0].id}`);

		// Check if assignment already exists
		const existingAssignment = await db
			.select()
			.from(userRoleAssignments)
			.where(
				and(
					eq(userRoleAssignments.userId, userId),
					eq(userRoleAssignments.roleId, role[0].id),
				),
			)
			.limit(1);

		if (existingAssignment.length) {
			console.log(`Updating existing role assignment for user ${userId}`);
			// Update existing assignment to active
			await db
				.update(userRoleAssignments)
				.set({
					isActive: true,
					assignedAt: new Date(),
					assignedBy,
				})
				.where(eq(userRoleAssignments.id, existingAssignment[0].id));
		} else {
			console.log(`Creating new role assignment for user ${userId}`);
			// Create new assignment
			const newAssignment = await db
				.insert(userRoleAssignments)
				.values({
					userId,
					roleId: role[0].id,
					assignedBy,
					isActive: true,
				})
				.returning();
			console.log(`Created role assignment with ID: ${newAssignment[0]?.id}`);
		}

		console.log(`Successfully assigned role ${roleName} to user ${userId}`);
		return true;
	} catch (error) {
		console.error("Error assigning role to user:", error);
		return false;
	}
}

/**
 * Remove role from user
 */
export async function removeRoleFromUser(
	userId: string,
	roleName: RoleType,
): Promise<boolean> {
	try {
		// Get the role ID
		const role = await db
			.select()
			.from(userRoles)
			.where(eq(userRoles.name, roleName))
			.limit(1);

		if (!role.length) {
			return false;
		}

		// Deactivate the assignment
		await db
			.update(userRoleAssignments)
			.set({ isActive: false })
			.where(
				and(
					eq(userRoleAssignments.userId, userId),
					eq(userRoleAssignments.roleId, role[0].id),
				),
			);

		return true;
	} catch (error) {
		console.error("Error removing role from user:", error);
		return false;
	}
}
