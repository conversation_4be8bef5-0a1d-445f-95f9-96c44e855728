import { getSubdomainForRole, getUserPrimaryRole } from "./roles";
import type { RoleType } from "./types";

/**
 * Get the appropriate redirect URL based on user's role
 */
export async function getRoleBasedRedirectUrl(
	userId: string,
	baseUrl?: string,
	fallbackPath = "/dashboard",
): Promise<string> {
	try {
		const primaryRole = await getUserPrimaryRole(userId);

		if (!primaryRole) {
			// No role assigned, redirect to main domain dashboard
			return baseUrl ? `${baseUrl}${fallbackPath}` : fallbackPath;
		}

		const subdomain = getSubdomainForRole(primaryRole);

		// Determine the base URL based on environment
		let redirectBaseUrl: string;

		if (baseUrl) {
			redirectBaseUrl = baseUrl;
		} else if (typeof window !== "undefined") {
			// Client-side
			const protocol = window.location.protocol;
			const hostname = window.location.hostname;

			if (process.env.NODE_ENV === "production") {
				// In production, use the subdomain
				redirectBaseUrl = `${protocol}//${subdomain}.learnfunda.ai`;
			} else {
				// In development, use localhost with port
				const port = window.location.port ? `:${window.location.port}` : "";
				redirectBaseUrl = `${protocol}//${hostname}${port}`;
			}
		} else {
			// Server-side fallback
			if (process.env.NODE_ENV === "production") {
				redirectBaseUrl = `https://${subdomain}.learnfunda.ai`;
			} else {
				redirectBaseUrl = "http://localhost:3000";
			}
		}

		return `${redirectBaseUrl}${fallbackPath}`;
	} catch (error) {
		console.error("Error getting role-based redirect URL:", error);
		// Fallback to provided base URL or current domain
		return baseUrl ? `${baseUrl}${fallbackPath}` : fallbackPath;
	}
}

/**
 * Get the subdomain URL for a specific role
 */
export function getSubdomainUrl(role: RoleType, path = "/dashboard"): string {
	const subdomain = getSubdomainForRole(role);

	if (process.env.NODE_ENV === "production") {
		return `https://${subdomain}.learnfunda.ai${path}`;
	} else {
		return `http://localhost:3000${path}`;
	}
}

/**
 * Check if current domain matches user's role
 */
export async function isOnCorrectSubdomain(
	userId: string,
	currentHostname: string,
): Promise<boolean> {
	try {
		const primaryRole = await getUserPrimaryRole(userId);

		if (!primaryRole) {
			return true; // No role restriction
		}

		const expectedSubdomain = getSubdomainForRole(primaryRole);

		if (process.env.NODE_ENV === "production") {
			const expectedHostname = `${expectedSubdomain}.learnfunda.ai`;
			return (
				currentHostname === expectedHostname ||
				currentHostname === "learnfunda.ai"
			);
		} else {
			// In development, all subdomains are served from localhost
			return currentHostname.includes("localhost");
		}
	} catch (error) {
		console.error("Error checking subdomain:", error);
		return true; // Allow access on error
	}
}

/**
 * Redirect user to their appropriate subdomain
 */
export async function redirectToUserSubdomain(
	userId: string,
	currentUrl: string,
	preservePath = true,
): Promise<string | null> {
	try {
		const primaryRole = await getUserPrimaryRole(userId);

		if (!primaryRole) {
			return null; // No redirection needed
		}

		const url = new URL(currentUrl);
		const currentSubdomain = url.hostname.split(".")[0];
		const expectedSubdomain = getSubdomainForRole(primaryRole);

		// Check if already on correct subdomain
		if (currentSubdomain === expectedSubdomain) {
			return null; // Already on correct subdomain
		}

		// Build redirect URL
		if (process.env.NODE_ENV === "production") {
			url.hostname = `${expectedSubdomain}.learnfunda.ai`;
		}

		if (!preservePath) {
			url.pathname = "/dashboard";
			url.search = "";
		}

		return url.toString();
	} catch (error) {
		console.error("Error redirecting to user subdomain:", error);
		return null;
	}
}

/**
 * Get login success redirect URL with role-based routing
 */
export async function getLoginSuccessRedirect(
	userId: string,
	requestedPath?: string,
	requestedSubdomain?: string,
): Promise<string> {
	try {
		const primaryRole = await getUserPrimaryRole(userId);

		if (!primaryRole) {
			// No role, redirect to main dashboard
			return requestedPath || "/dashboard";
		}

		const userSubdomain = getSubdomainForRole(primaryRole);

		// If user requested a specific subdomain, validate they have access
		if (requestedSubdomain && requestedSubdomain !== userSubdomain) {
			// User requested wrong subdomain, redirect to their correct one
			return getSubdomainUrl(primaryRole, requestedPath || "/dashboard");
		}

		// If no specific subdomain requested, redirect to user's subdomain
		if (!requestedSubdomain) {
			return getSubdomainUrl(primaryRole, requestedPath || "/dashboard");
		}

		// User has access to requested subdomain
		return requestedPath || "/dashboard";
	} catch (error) {
		console.error("Error getting login success redirect:", error);
		return requestedPath || "/dashboard";
	}
}
