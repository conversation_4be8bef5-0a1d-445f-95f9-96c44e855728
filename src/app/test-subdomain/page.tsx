"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle, <PERSON>ie, Globe, Shield } from "lucide-react";
import { useEffect, useState } from "react";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { useSession } from "@/lib/auth-client";

interface CookieInfo {
	cookies: Record<string, string>;
	domain: string;
	protocol: string;
	userAgent: string;
}

export default function TestSubdomainPage() {
	const { data: session, isPending } = useSession();
	const [cookieInfo, setCookieInfo] = useState<CookieInfo | null>(null);

	useEffect(() => {
		// Get cookie information
		const cookies = document.cookie
			.split(";")
			.reduce((acc: Record<string, string>, cookie) => {
				const [name, value] = cookie.trim().split("=");
				if (name.includes("better-auth")) {
					acc[name] = value;
				}
				return acc;
			}, {});

		setCookieInfo({
			cookies,
			domain: window.location.hostname,
			protocol: window.location.protocol,
			userAgent: navigator.userAgent,
		});
	}, []);

	if (isPending) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-gray-50">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
					<p className="text-gray-600">Loading...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gray-50">
			{/* Header */}
			<header className="bg-white shadow-sm border-b">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex justify-between items-center h-16">
						<div className="flex items-center">
							<div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
								<span className="text-white font-bold text-sm">L</span>
							</div>
							<span className="ml-3 text-xl font-semibold text-gray-800">
								LearnFunda - Subdomain Test
							</span>
						</div>
					</div>
				</div>
			</header>

			{/* Main Content */}
			<main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900">
						Cross-Subdomain Authentication Test
					</h1>
					<p className="text-gray-600 mt-2">
						Testing authentication configuration for cross-subdomain
						functionality
					</p>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
					{/* Authentication Status */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Shield className="h-5 w-5 text-green-600" />
								Authentication Status
							</CardTitle>
							<CardDescription>Current session information</CardDescription>
						</CardHeader>
						<CardContent className="space-y-3">
							<div className="flex items-center gap-2">
								{session ? (
									<>
										<CheckCircle className="h-4 w-4 text-green-600" />
										<span className="text-sm">Authenticated</span>
									</>
								) : (
									<>
										<AlertCircle className="h-4 w-4 text-red-600" />
										<span className="text-sm">Not authenticated</span>
									</>
								)}
							</div>
							{session && (
								<>
									<div>
										<span className="text-sm font-medium text-gray-500">
											Email
										</span>
										<p className="text-gray-900">{session.user.email}</p>
									</div>
									<div>
										<span className="text-sm font-medium text-gray-500">
											Session ID
										</span>
										<p className="text-gray-900 font-mono text-xs truncate">
											{session.session.id}
										</p>
									</div>
								</>
							)}
						</CardContent>
					</Card>

					{/* Cookie Configuration */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Cookie className="h-5 w-5 text-blue-600" />
								Cookie Configuration
							</CardTitle>
							<CardDescription>Cross-subdomain cookie settings</CardDescription>
						</CardHeader>
						<CardContent className="space-y-3">
							<div className="flex items-center gap-2">
								<CheckCircle className="h-4 w-4 text-green-600" />
								<span className="text-sm">Cross-subdomain enabled</span>
							</div>
							<div className="flex items-center gap-2">
								<CheckCircle className="h-4 w-4 text-green-600" />
								<span className="text-sm">Domain: .learnfunda.ai</span>
							</div>
							<div className="flex items-center gap-2">
								<CheckCircle className="h-4 w-4 text-green-600" />
								<span className="text-sm">SameSite: lax</span>
							</div>
							<div className="flex items-center gap-2">
								<CheckCircle className="h-4 w-4 text-green-600" />
								<span className="text-sm">Secure in production</span>
							</div>
						</CardContent>
					</Card>

					{/* Environment Info */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Globe className="h-5 w-5 text-purple-600" />
								Environment Info
							</CardTitle>
							<CardDescription>Current environment details</CardDescription>
						</CardHeader>
						<CardContent className="space-y-3">
							{cookieInfo && (
								<>
									<div>
										<span className="text-sm font-medium text-gray-500">
											Domain
										</span>
										<p className="text-gray-900">{cookieInfo.domain}</p>
									</div>
									<div>
										<span className="text-sm font-medium text-gray-500">
											Protocol
										</span>
										<p className="text-gray-900">{cookieInfo.protocol}</p>
									</div>
									<div>
										<span className="text-sm font-medium text-gray-500">
											Auth Cookies
										</span>
										<p className="text-gray-900 text-xs">
											{Object.keys(cookieInfo.cookies).length > 0
												? `${Object.keys(cookieInfo.cookies).length} found`
												: "None found"}
										</p>
									</div>
								</>
							)}
						</CardContent>
					</Card>
				</div>

				{/* 4 Subdomains Configuration */}
				<Card className="mb-8">
					<CardHeader>
						<CardTitle>4-Subdomain Architecture</CardTitle>
						<CardDescription>
							LearnFunda supports 4 different user types with dedicated
							subdomains
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<h4 className="font-semibold text-gray-900 mb-3">
									🎓 Student Portal
								</h4>
								<div className="bg-blue-50 p-3 rounded-lg mb-3">
									<p className="font-medium text-blue-800">
										students.learnfunda.ai
									</p>
									<p className="text-sm text-blue-600">
										Access courses, assignments, grades
									</p>
								</div>
								<ul className="space-y-1 text-sm text-gray-700">
									<li>• Dashboard</li>
									<li>• Courses</li>
									<li>• Assignments</li>
									<li>• Grades</li>
									<li>• Profile</li>
								</ul>
							</div>
							<div>
								<h4 className="font-semibold text-gray-900 mb-3">
									👨‍🏫 Teacher Portal
								</h4>
								<div className="bg-green-50 p-3 rounded-lg mb-3">
									<p className="font-medium text-green-800">
										teachers.learnfunda.ai
									</p>
									<p className="text-sm text-green-600">
										Manage classes, students, gradebook
									</p>
								</div>
								<ul className="space-y-1 text-sm text-gray-700">
									<li>• Dashboard</li>
									<li>• Classes</li>
									<li>• Students</li>
									<li>• Assignments</li>
									<li>• Gradebook</li>
									<li>• Profile</li>
								</ul>
							</div>
							<div>
								<h4 className="font-semibold text-gray-900 mb-3">
									👨‍💼 Admin Portal
								</h4>
								<div className="bg-purple-50 p-3 rounded-lg mb-3">
									<p className="font-medium text-purple-800">
										admins.learnfunda.ai
									</p>
									<p className="text-sm text-purple-600">
										System administration and analytics
									</p>
								</div>
								<ul className="space-y-1 text-sm text-gray-700">
									<li>• Dashboard</li>
									<li>• Users</li>
									<li>• Schools</li>
									<li>• Analytics</li>
									<li>• Settings</li>
									<li>• Profile</li>
								</ul>
							</div>
							<div>
								<h4 className="font-semibold text-gray-900 mb-3">
									👨‍👩‍👧‍👦 Parent Portal
								</h4>
								<div className="bg-orange-50 p-3 rounded-lg mb-3">
									<p className="font-medium text-orange-800">
										parents.learnfunda.ai
									</p>
									<p className="text-sm text-orange-600">
										Monitor children's progress
									</p>
								</div>
								<ul className="space-y-1 text-sm text-gray-700">
									<li>• Dashboard</li>
									<li>• Children</li>
									<li>• Progress</li>
									<li>• Communication</li>
									<li>• Profile</li>
								</ul>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Configuration Details */}
				<Card className="mb-8">
					<CardHeader>
						<CardTitle>Cross-Subdomain Configuration Details</CardTitle>
						<CardDescription>
							BetterAuth configuration for 4-subdomain support
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<h4 className="font-semibold text-gray-900 mb-3">
									✅ Configured Features
								</h4>
								<ul className="space-y-2 text-sm text-gray-700">
									<li className="flex items-center gap-2">
										<CheckCircle className="h-4 w-4 text-green-600" />
										Cross-subdomain cookies enabled
									</li>
									<li className="flex items-center gap-2">
										<CheckCircle className="h-4 w-4 text-green-600" />
										Domain set to .learnfunda.ai
									</li>
									<li className="flex items-center gap-2">
										<CheckCircle className="h-4 w-4 text-green-600" />4
										subdomains configured
									</li>
									<li className="flex items-center gap-2">
										<CheckCircle className="h-4 w-4 text-green-600" />
										Role-based route protection
									</li>
									<li className="flex items-center gap-2">
										<CheckCircle className="h-4 w-4 text-green-600" />
										CSRF protection with lax SameSite
									</li>
									<li className="flex items-center gap-2">
										<CheckCircle className="h-4 w-4 text-green-600" />
										Secure cookies in production
									</li>
								</ul>
							</div>
							<div>
								<h4 className="font-semibold text-gray-900 mb-3">
									🧪 Test Scenarios
								</h4>
								<ul className="space-y-2 text-sm text-gray-700">
									<li>• Login on main domain (learnfunda.ai)</li>
									<li>• Navigate to students.learnfunda.ai</li>
									<li>• Navigate to teachers.learnfunda.ai</li>
									<li>• Navigate to admins.learnfunda.ai</li>
									<li>• Navigate to parents.learnfunda.ai</li>
									<li>• Session persists across all subdomains</li>
									<li>• Role-based access control enforced</li>
								</ul>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Success Message */}
				<div className="p-6 bg-green-50 border border-green-200 rounded-lg">
					<h3 className="text-lg font-semibold text-green-800 mb-2">
						🎉 4-Subdomain Authentication System Complete!
					</h3>
					<p className="text-green-700 mb-3">
						The authentication system is properly configured for 4-subdomain
						architecture:
					</p>
					<ul className="list-disc list-inside text-green-700 space-y-1">
						<li>BetterAuth configured with cross-subdomain cookie support</li>
						<li>4 dedicated subdomains: students, teachers, admins, parents</li>
						<li>Role-based route protection for each subdomain</li>
						<li>Domain set to .learnfunda.ai for wildcard subdomain access</li>
						<li>Trusted origins include all 4 LearnFunda subdomains</li>
						<li>CSRF protection configured for cross-subdomain requests</li>
						<li>Session persistence across all subdomain navigation</li>
						<li>Middleware enforces subdomain-specific access control</li>
						<li>Ready for production deployment with secure cookies</li>
					</ul>
				</div>

				<div className="mt-6">
					<a
						href="/dashboard"
						className="text-blue-600 hover:text-blue-800 underline"
					>
						← Back to Dashboard
					</a>
				</div>
			</main>
		</div>
	);
}
