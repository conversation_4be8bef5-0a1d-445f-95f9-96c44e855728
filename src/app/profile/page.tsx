import {
	dehydrate,
	HydrationBoundary,
	QueryClient,
} from "@tanstack/react-query";
import { headers } from "next/headers";
import { Suspense } from "react";
import StudentLayout from "@/components/layouts/StudentLayout";
import { auth } from "@/lib/auth";
import { getUserWithRoles } from "@/lib/auth/roles";
import { db } from "@/lib/db";
import { appRouter } from "@/server/api/root";
import { createCallerFactory } from "@/server/api/trpc";
import ProfileClient from "./profile-client";

// Server component that prefetches profile data
export default async function ProfilePage() {
	const queryClient = new QueryClient();

	try {
		// Get session from server
		const headersList = await headers();
		const session = await auth.api.getSession({
			headers: headersList as Headers,
		});

		if (!session?.user) {
			return (
				<StudentLayout>
					<div className="flex items-center justify-center min-h-96">
						<div className="text-center">
							<h1 className="text-2xl font-bold text-gray-900 mb-4">
								Authentication Required
							</h1>
							<p className="text-gray-600">
								Please sign in to view your profile.
							</p>
						</div>
					</div>
				</StudentLayout>
			);
		}

		// Check if user has STUDENT role
		const userWithRoles = await getUserWithRoles(session.user.id);
		const isStudent = userWithRoles?.roles?.some(
			(role) => role.name === "STUDENT",
		);

		if (!isStudent) {
			return (
				<StudentLayout>
					<div className="flex items-center justify-center min-h-96">
						<div className="text-center">
							<h1 className="text-2xl font-bold text-gray-900 mb-4">
								Access Denied
							</h1>
							<p className="text-gray-600">
								This page is only available for students.
							</p>
						</div>
					</div>
				</StudentLayout>
			);
		}

		// Create TRPC caller for server-side data fetching
		const createCaller = createCallerFactory(appRouter);
		const caller = createCaller({
			db,
			user: {
				id: session.user.id,
				name: session.user.name,
				email: session.user.email,
				emailVerified: session.user.emailVerified,
				image: session.user.image,
			},
			userWithRoles,
			req: { headers: headersList } as unknown as Request,
		});

		// Prefetch profile data
		await Promise.all([
			queryClient.prefetchQuery({
				queryKey: ["profile", "getMyProfile"],
				queryFn: () => caller.profile.getMyProfile(),
			}),
			queryClient.prefetchQuery({
				queryKey: ["profile", "getProfileStats"],
				queryFn: () => caller.profile.getProfileStats(),
			}),
		]);

		return (
			<StudentLayout>
				<HydrationBoundary state={dehydrate(queryClient)}>
					<Suspense fallback={<ProfileLoadingFallback />}>
						<ProfileClient />
					</Suspense>
				</HydrationBoundary>
			</StudentLayout>
		);
	} catch (error) {
		console.error("Error in profile page:", error);

		return (
			<StudentLayout>
				<div className="flex items-center justify-center min-h-96">
					<div className="text-center">
						<h1 className="text-2xl font-bold text-gray-900 mb-4">
							Something went wrong
						</h1>
						<p className="text-gray-600">
							Please try refreshing the page or contact support if the problem
							persists.
						</p>
					</div>
				</div>
			</StudentLayout>
		);
	}
}

// Loading fallback component
function ProfileLoadingFallback() {
	return (
		<div className="p-6 space-y-6">
			<div className="mb-8">
				<div className="h-8 bg-gray-200 rounded w-48 mb-2 animate-pulse" />
				<div className="h-4 bg-gray-200 rounded w-64 animate-pulse" />
			</div>

			{/* Profile completion card skeleton */}
			<div className="border rounded-lg p-6 animate-pulse">
				<div className="flex items-center justify-between mb-4">
					<div className="h-6 bg-gray-200 rounded w-40" />
					<div className="h-6 bg-gray-200 rounded w-16" />
				</div>
				<div className="h-2 bg-gray-200 rounded w-full mb-4" />
				<div className="h-4 bg-gray-200 rounded w-48" />
			</div>

			{/* Profile form skeleton */}
			<div className="border rounded-lg p-6 animate-pulse">
				<div className="h-6 bg-gray-200 rounded w-32 mb-6" />
				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					{[1, 2, 3, 4, 5, 6].map((i) => (
						<div key={i} className="space-y-2">
							<div className="h-4 bg-gray-200 rounded w-24" />
							<div className="h-10 bg-gray-200 rounded w-full" />
						</div>
					))}
				</div>
				<div className="flex justify-end mt-6">
					<div className="h-10 bg-gray-200 rounded w-32" />
				</div>
			</div>
		</div>
	);
}
