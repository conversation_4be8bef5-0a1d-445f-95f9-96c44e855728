"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { CheckCircle, Edit, Save, User, X } from "lucide-react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useSession } from "@/lib/auth-client";
import { api } from "@/trpc/client";

// Form validation schema
const profileSchema = z.object({
	fullName: z.string().min(1, "Full name is required").max(255),
	standard: z.enum(["8th", "9th", "10th"]),
	schoolName: z.string().min(1, "School name is required").max(255),
	educationalBoard: z.enum(["CBSE", "ICSE", "STATE_BOARD"]),
	interests: z.array(z.string()).optional(),
	dislikes: z.array(z.string()).optional(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

// Predefined interests and dislikes options
const INTEREST_OPTIONS = [
	"Mathematics",
	"Science",
	"Physics",
	"Chemistry",
	"Biology",
	"English",
	"Hindi",
	"History",
	"Geography",
	"Computer Science",
	"Art",
	"Music",
	"Sports",
	"Reading",
	"Writing",
	"Technology",
	"Gaming",
	"Movies",
	"Travel",
	"Cooking",
];

const DISLIKE_OPTIONS = [
	"Mathematics",
	"Science",
	"Physics",
	"Chemistry",
	"Biology",
	"English",
	"Hindi",
	"History",
	"Geography",
	"Computer Science",
	"Public Speaking",
	"Group Work",
	"Exams",
	"Homework",
	"Early Morning Classes",
];

export default function ProfileClient() {
	const { data: session } = useSession();
	const [isEditing, setIsEditing] = useState(false);
	const [selectedInterests, setSelectedInterests] = useState<string[]>([]);
	const [selectedDislikes, setSelectedDislikes] = useState<string[]>([]);

	// Fetch profile data
	const { data: profile, refetch: refetchProfile } =
		api.profile.getMyProfile.useQuery();
	const { data: profileStats } = api.profile.getProfileStats.useQuery();

	// Mutations
	const createProfileMutation = api.profile.createProfile.useMutation({
		onSuccess: () => {
			toast.success("Profile created successfully!");
			setIsEditing(false);
			refetchProfile();
		},
		onError: (error) => {
			toast.error(error.message);
		},
	});

	const updateProfileMutation = api.profile.updateProfile.useMutation({
		onSuccess: () => {
			toast.success("Profile updated successfully!");
			setIsEditing(false);
			refetchProfile();
		},
		onError: (error) => {
			toast.error(error.message);
		},
	});

	// Form setup
	const form = useForm<ProfileFormData>({
		resolver: zodResolver(profileSchema),
		defaultValues: {
			fullName: profile?.fullName || "",
			standard: profile?.standard || "8th",
			schoolName: profile?.schoolName || "",
			educationalBoard: profile?.educationalBoard || "CBSE",
			interests: profile?.interests || [],
			dislikes: profile?.dislikes || [],
		},
	});

	// Update form when profile data loads
	React.useEffect(() => {
		if (profile) {
			form.reset({
				fullName: profile.fullName,
				standard: profile.standard,
				schoolName: profile.schoolName,
				educationalBoard: profile.educationalBoard,
				interests: profile.interests || [],
				dislikes: profile.dislikes || [],
			});
			setSelectedInterests(profile.interests || []);
			setSelectedDislikes(profile.dislikes || []);
		}
	}, [profile, form]);

	const onSubmit = (data: ProfileFormData) => {
		const profileData = {
			...data,
			interests: selectedInterests,
			dislikes: selectedDislikes,
		};

		if (profile) {
			updateProfileMutation.mutate(profileData);
		} else {
			createProfileMutation.mutate(profileData);
		}
	};

	const handleInterestToggle = (interest: string) => {
		setSelectedInterests((prev) =>
			prev.includes(interest)
				? prev.filter((i) => i !== interest)
				: [...prev, interest],
		);
	};

	const handleDislikeToggle = (dislike: string) => {
		setSelectedDislikes((prev) =>
			prev.includes(dislike)
				? prev.filter((d) => d !== dislike)
				: [...prev, dislike],
		);
	};

	const isLoading =
		createProfileMutation.isPending || updateProfileMutation.isPending;

	return (
		<div className="p-6 space-y-6">
			{/* Header */}
			<div className="mb-8">
				<h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
				<p className="text-gray-600 mt-2">
					Manage your personal information and learning preferences
				</p>
			</div>

			{/* Profile Completion Card */}
			{profileStats && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<User className="h-5 w-5" />
							Profile Completion
						</CardTitle>
						<CardDescription>
							Complete your profile to get personalized learning recommendations
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">
									{profileStats.completionPercentage}% Complete
								</span>
								{profileStats.isComplete && (
									<Badge variant="default" className="bg-green-600">
										<CheckCircle className="h-3 w-3 mr-1" />
										Complete
									</Badge>
								)}
							</div>
							<Progress
								value={profileStats.completionPercentage}
								className="h-2"
							/>
							{profileStats.missingFields.length > 0 && (
								<p className="text-sm text-gray-600">
									Missing: {profileStats.missingFields.join(", ")}
								</p>
							)}
						</div>
					</CardContent>
				</Card>
			)}

			{/* Profile Form */}
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle>Personal Information</CardTitle>
							<CardDescription>
								Your basic information and educational details
							</CardDescription>
						</div>
						{!isEditing && (
							<Button
								variant="outline"
								size="sm"
								onClick={() => setIsEditing(true)}
							>
								<Edit className="h-4 w-4 mr-2" />
								Edit
							</Button>
						)}
					</div>
				</CardHeader>
				<CardContent>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							{/* Full Name */}
							<div className="space-y-2">
								<Label htmlFor="fullName">Full Name</Label>
								<Input
									id="fullName"
									{...form.register("fullName")}
									disabled={!isEditing || isLoading}
									placeholder="Enter your full name"
								/>
								{form.formState.errors.fullName && (
									<p className="text-sm text-red-500">
										{form.formState.errors.fullName.message}
									</p>
								)}
							</div>

							{/* Email (read-only) */}
							<div className="space-y-2">
								<Label htmlFor="email">Email</Label>
								<Input
									id="email"
									value={session?.user?.email || ""}
									disabled
									className="bg-gray-50"
								/>
							</div>

							{/* Standard */}
							<div className="space-y-2">
								<Label htmlFor="standard">Standard</Label>
								<Select
									value={form.watch("standard")}
									onValueChange={(value) =>
										form.setValue("standard", value as "8th" | "9th" | "10th")
									}
									disabled={!isEditing || isLoading}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select your standard" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="8th">8th Standard</SelectItem>
										<SelectItem value="9th">9th Standard</SelectItem>
										<SelectItem value="10th">10th Standard</SelectItem>
									</SelectContent>
								</Select>
							</div>

							{/* Educational Board */}
							<div className="space-y-2">
								<Label htmlFor="educationalBoard">Educational Board</Label>
								<Select
									value={form.watch("educationalBoard")}
									onValueChange={(value) =>
										form.setValue(
											"educationalBoard",
											value as "CBSE" | "ICSE" | "STATE_BOARD",
										)
									}
									disabled={!isEditing || isLoading}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select your board" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="CBSE">CBSE</SelectItem>
										<SelectItem value="ICSE">ICSE</SelectItem>
										<SelectItem value="STATE_BOARD">State Board</SelectItem>
									</SelectContent>
								</Select>
							</div>

							{/* School Name */}
							<div className="space-y-2 md:col-span-2">
								<Label htmlFor="schoolName">School Name</Label>
								<Input
									id="schoolName"
									{...form.register("schoolName")}
									disabled={!isEditing || isLoading}
									placeholder="Enter your school name"
								/>
								{form.formState.errors.schoolName && (
									<p className="text-sm text-red-500">
										{form.formState.errors.schoolName.message}
									</p>
								)}
							</div>
						</div>

						{/* Interests */}
						{isEditing && (
							<div className="space-y-4">
								<div>
									<Label>Interests (Optional)</Label>
									<p className="text-sm text-gray-600 mb-3">
										Select subjects and topics you enjoy learning about
									</p>
									<div className="flex flex-wrap gap-2">
										{INTEREST_OPTIONS.map((interest) => (
											<Badge
												key={interest}
												variant={
													selectedInterests.includes(interest)
														? "default"
														: "outline"
												}
												className="cursor-pointer"
												onClick={() => handleInterestToggle(interest)}
											>
												{interest}
												{selectedInterests.includes(interest) && (
													<X className="h-3 w-3 ml-1" />
												)}
											</Badge>
										))}
									</div>
								</div>

								<div>
									<Label>Dislikes (Optional)</Label>
									<p className="text-sm text-gray-600 mb-3">
										Select subjects or activities you find challenging
									</p>
									<div className="flex flex-wrap gap-2">
										{DISLIKE_OPTIONS.map((dislike) => (
											<Badge
												key={dislike}
												variant={
													selectedDislikes.includes(dislike)
														? "destructive"
														: "outline"
												}
												className="cursor-pointer"
												onClick={() => handleDislikeToggle(dislike)}
											>
												{dislike}
												{selectedDislikes.includes(dislike) && (
													<X className="h-3 w-3 ml-1" />
												)}
											</Badge>
										))}
									</div>
								</div>
							</div>
						)}

						{/* Display current interests and dislikes when not editing */}
						{!isEditing && profile && (
							<div className="space-y-4">
								{profile.interests && profile.interests.length > 0 && (
									<div>
										<Label>Interests</Label>
										<div className="flex flex-wrap gap-2 mt-2">
											{profile.interests.map((interest: string) => (
												<Badge key={interest} variant="default">
													{interest}
												</Badge>
											))}
										</div>
									</div>
								)}

								{profile.dislikes && profile.dislikes.length > 0 && (
									<div>
										<Label>Dislikes</Label>
										<div className="flex flex-wrap gap-2 mt-2">
											{profile.dislikes.map((dislike: string) => (
												<Badge key={dislike} variant="destructive">
													{dislike}
												</Badge>
											))}
										</div>
									</div>
								)}
							</div>
						)}

						{/* Action Buttons */}
						{isEditing && (
							<div className="flex justify-end gap-3">
								<Button
									type="button"
									variant="outline"
									onClick={() => {
										setIsEditing(false);
										form.reset();
										setSelectedInterests(profile?.interests || []);
										setSelectedDislikes(profile?.dislikes || []);
									}}
									disabled={isLoading}
								>
									Cancel
								</Button>
								<Button type="submit" disabled={isLoading}>
									<Save className="h-4 w-4 mr-2" />
									{isLoading ? "Saving..." : "Save Changes"}
								</Button>
							</div>
						)}
					</form>
				</CardContent>
			</Card>
		</div>
	);
}
