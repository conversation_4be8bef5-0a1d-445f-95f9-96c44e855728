export default function SettingsPage() {
	return (
		<div className="min-h-screen bg-gray-50 flex items-center justify-center">
			<div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
				<h1 className="text-2xl font-bold text-gray-900 mb-4">Settings Page</h1>
				<div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
					<p className="text-blue-700">
						🔧 Settings page successfully loaded! This is another protected
						route.
					</p>
					<p className="text-blue-600 text-sm mt-2">
						The middleware is working correctly to protect all defined routes.
					</p>
				</div>
				<div className="mt-4">
					<a
						href="/dashboard"
						className="text-blue-600 hover:text-blue-800 underline"
					>
						← Back to Dashboard
					</a>
				</div>
			</div>
		</div>
	);
}
