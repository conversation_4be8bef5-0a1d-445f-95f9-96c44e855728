import { Resend } from "resend";
import { env } from "@/env";

const resend = new Resend(env.RESEND_API_KEY || "dummy-key-for-build");
export async function POST(request: Request) {
	const { email } = await request.json();

	if (!email || !email.includes("@")) {
		return Response.json({ error: "Valid email is required" }, { status: 400 });
	}

	if (!env.RESEND_API_KEY || env.RESEND_API_KEY === "dummy-key-for-build") {
		return Response.json(
			{ error: "Email service not configured" },
			{ status: 500 },
		);
	}

	try {
		const { error } = await resend.emails.send({
			from: "LearnFunda <<EMAIL>>", // Replace with your verified domain
			to: [email],
			subject: "Welcome to LearnFunda - You're on the list!",
			html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%); padding: 40px 20px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to LearnFunda!</h1>
          </div>
          <div style="background: white; padding: 30px 20px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #1E40AF; margin-bottom: 20px;">Thanks for joining our waitlist!</h2>
            <p style="color: #4B5563; line-height: 1.6; margin-bottom: 20px;">
              We're excited to have you on board. You'll be among the first to know when we launch our powerful learning platform.
            </p>
            <p style="color: #4B5563; line-height: 1.6; margin-bottom: 20px;">
              Get ready for an enhanced learning experience that's designed just for you.
            </p>
            <div style="background: #F3F4F6; padding: 20px; border-radius: 8px; margin-top: 30px;">
              <p style="color: #6B7280; margin: 0; font-size: 14px; text-align: center;">
                Follow us on social media for updates and learning tips!
              </p>
            </div>
          </div>
        </div>
      `,
		});

		if (error) {
			console.error("Resend error:", error);
			return Response.json({ error: "Failed to send email" }, { status: 500 });
		}

		// Notification email to admin
		await resend.emails.send({
			from: "LearnFunda <<EMAIL>>", // Replace with your verified domain
			to: ["<EMAIL>", "<EMAIL>"], // Replace with your admin email
			subject: "New LearnFunda Subscriber",
			html: `
        <h2>New Subscriber Alert</h2>
        <p>A new user has subscribed to the LearnFunda waitlist:</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Time:</strong> ${new Date().toISOString()}</p>
      `,
		});

		return Response.json({ message: "Successfully subscribed!" });
	} catch (error) {
		console.error("API error:", error);
		return Response.json({ error: "Internal server error" }, { status: 500 });
	}
}
