import type { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { ResetPasswordForm } from "@/components/auth/reset-password-form";

export const metadata: Metadata = {
	title: "Reset Password | LearnFunda",
	description: "Reset your LearnFunda account password",
};

function ResetPasswordWrapper() {
	return <ResetPasswordForm />;
}

export default function ResetPasswordPage() {
	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
			<div className="w-full max-w-md">
				<Suspense fallback={<div>Loading...</div>}>
					<ResetPasswordWrapper />
				</Suspense>
			</div>
		</div>
	);
}
