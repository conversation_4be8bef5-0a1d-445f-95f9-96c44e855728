import type { Metada<PERSON> } from "next";
import { Suspense } from "react";
import { EmailVerification } from "@/components/auth/email-verification";

export const metadata: Metadata = {
	title: "Verify Email | LearnFunda",
	description:
		"Verify your email address to complete your LearnFunda account setup",
};

function EmailVerificationWrapper() {
	return <EmailVerification />;
}

export default function VerifyEmailPage() {
	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
			<div className="w-full max-w-md">
				<Suspense fallback={<div>Loading...</div>}>
					<EmailVerificationWrapper />
				</Suspense>
			</div>
		</div>
	);
}
