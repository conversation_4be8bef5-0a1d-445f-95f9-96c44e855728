@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--font-sans: Inter, sans-serif;
	--font-mono: JetBrains Mono, monospace;
	--color-sidebar-ring: var(--sidebar-ring);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar: var(--sidebar);
	--color-chart-5: var(--chart-5);
	--color-chart-4: var(--chart-4);
	--color-chart-3: var(--chart-3);
	--color-chart-2: var(--chart-2);
	--color-chart-1: var(--chart-1);
	--color-ring: var(--ring);
	--color-input: var(--input);
	--color-border: var(--border);
	--color-destructive: var(--destructive);
	--color-accent-foreground: var(--accent-foreground);
	--color-accent: var(--accent);
	--color-muted-foreground: var(--muted-foreground);
	--color-muted: var(--muted);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-secondary: var(--secondary);
	--color-primary-foreground: var(--primary-foreground);
	--color-primary: var(--primary);
	--color-popover-foreground: var(--popover-foreground);
	--color-popover: var(--popover);
	--color-card-foreground: var(--card-foreground);
	--color-card: var(--card);
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
	--font-serif: Merriweather, serif;
	--radius: 0.5rem;
	--tracking-tighter: calc(var(--tracking-normal) - 0.05em);
	--tracking-tight: calc(var(--tracking-normal) - 0.025em);
	--tracking-wide: calc(var(--tracking-normal) + 0.025em);
	--tracking-wider: calc(var(--tracking-normal) + 0.05em);
	--tracking-widest: calc(var(--tracking-normal) + 0.1em);
	--tracking-normal: var(--tracking-normal);
	--shadow-2xl: var(--shadow-2xl);
	--shadow-xl: var(--shadow-xl);
	--shadow-lg: var(--shadow-lg);
	--shadow-md: var(--shadow-md);
	--shadow: var(--shadow);
	--shadow-sm: var(--shadow-sm);
	--shadow-xs: var(--shadow-xs);
	--shadow-2xs: var(--shadow-2xs);
	--spacing: var(--spacing);
	--letter-spacing: var(--letter-spacing);
	--shadow-offset-y: var(--shadow-offset-y);
	--shadow-offset-x: var(--shadow-offset-x);
	--shadow-spread: var(--shadow-spread);
	--shadow-blur: var(--shadow-blur);
	--shadow-opacity: var(--shadow-opacity);
	--color-shadow-color: var(--shadow-color);
	--color-destructive-foreground: var(--destructive-foreground);
}

:root {
	--radius: 0.5rem;
	--background: oklch(0.9842 0.0034 247.8575);
	--foreground: oklch(0.2795 0.0368 260.031);
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.2795 0.0368 260.031);
	--popover: oklch(1 0 0);
	--popover-foreground: oklch(0.2795 0.0368 260.031);
	--primary: oklch(0.5854 0.2041 277.1173);
	--primary-foreground: oklch(1 0 0);
	--secondary: oklch(0.9276 0.0058 264.5313);
	--secondary-foreground: oklch(0.3729 0.0306 259.7328);
	--muted: oklch(0.967 0.0029 264.5419);
	--muted-foreground: oklch(0.551 0.0234 264.3637);
	--accent: oklch(0.9299 0.0334 272.7879);
	--accent-foreground: oklch(0.3729 0.0306 259.7328);
	--destructive: oklch(0.6368 0.2078 25.3313);
	--border: oklch(0.8717 0.0093 258.3382);
	--input: oklch(0.8717 0.0093 258.3382);
	--ring: oklch(0.5854 0.2041 277.1173);
	--chart-1: oklch(0.5854 0.2041 277.1173);
	--chart-2: oklch(0.5106 0.2301 276.9656);
	--chart-3: oklch(0.4568 0.2146 277.0229);
	--chart-4: oklch(0.3984 0.1773 277.3662);
	--chart-5: oklch(0.3588 0.1354 278.6973);
	--sidebar: oklch(0.967 0.0029 264.5419);
	--sidebar-foreground: oklch(0.2795 0.0368 260.031);
	--sidebar-primary: oklch(0.5854 0.2041 277.1173);
	--sidebar-primary-foreground: oklch(1 0 0);
	--sidebar-accent: oklch(0.9299 0.0334 272.7879);
	--sidebar-accent-foreground: oklch(0.3729 0.0306 259.7328);
	--sidebar-border: oklch(0.8717 0.0093 258.3382);
	--sidebar-ring: oklch(0.5854 0.2041 277.1173);
	--destructive-foreground: oklch(1 0 0);
	--font-sans: Inter, sans-serif;
	--font-serif: Merriweather, serif;
	--font-mono: JetBrains Mono, monospace;
	--shadow-color: hsl(0 0% 0%);
	--shadow-opacity: 0.1;
	--shadow-blur: 8px;
	--shadow-spread: -1px;
	--shadow-offset-x: 0px;
	--shadow-offset-y: 4px;
	--letter-spacing: 0em;
	--spacing: 0.25rem;
	--shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
	--shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
	--shadow-sm:
		0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 1px 2px -2px hsl(0 0% 0% / 0.1);
	--shadow:
		0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 1px 2px -2px hsl(0 0% 0% / 0.1);
	--shadow-md:
		0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 2px 4px -2px hsl(0 0% 0% / 0.1);
	--shadow-lg:
		0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 4px 6px -2px hsl(0 0% 0% / 0.1);
	--shadow-xl:
		0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 8px 10px -2px hsl(0 0% 0% / 0.1);
	--shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
	--tracking-normal: 0em;
}

.dark {
	--background: oklch(0.2077 0.0398 265.7549);
	--foreground: oklch(0.9288 0.0126 255.5078);
	--card: oklch(0.2795 0.0368 260.031);
	--card-foreground: oklch(0.9288 0.0126 255.5078);
	--popover: oklch(0.2795 0.0368 260.031);
	--popover-foreground: oklch(0.9288 0.0126 255.5078);
	--primary: oklch(0.6801 0.1583 276.9349);
	--primary-foreground: oklch(0.2077 0.0398 265.7549);
	--secondary: oklch(0.3351 0.0331 260.912);
	--secondary-foreground: oklch(0.8717 0.0093 258.3382);
	--muted: oklch(0.2795 0.0368 260.031);
	--muted-foreground: oklch(0.7137 0.0192 261.3246);
	--accent: oklch(0.3729 0.0306 259.7328);
	--accent-foreground: oklch(0.8717 0.0093 258.3382);
	--destructive: oklch(0.6368 0.2078 25.3313);
	--border: oklch(0.4461 0.0263 256.8018);
	--input: oklch(0.4461 0.0263 256.8018);
	--ring: oklch(0.6801 0.1583 276.9349);
	--chart-1: oklch(0.6801 0.1583 276.9349);
	--chart-2: oklch(0.5854 0.2041 277.1173);
	--chart-3: oklch(0.5106 0.2301 276.9656);
	--chart-4: oklch(0.4568 0.2146 277.0229);
	--chart-5: oklch(0.3984 0.1773 277.3662);
	--sidebar: oklch(0.2795 0.0368 260.031);
	--sidebar-foreground: oklch(0.9288 0.0126 255.5078);
	--sidebar-primary: oklch(0.6801 0.1583 276.9349);
	--sidebar-primary-foreground: oklch(0.2077 0.0398 265.7549);
	--sidebar-accent: oklch(0.3729 0.0306 259.7328);
	--sidebar-accent-foreground: oklch(0.8717 0.0093 258.3382);
	--sidebar-border: oklch(0.4461 0.0263 256.8018);
	--sidebar-ring: oklch(0.6801 0.1583 276.9349);
	--destructive-foreground: oklch(0.2077 0.0398 265.7549);
	--radius: 0.5rem;
	--font-sans: Inter, sans-serif;
	--font-serif: Merriweather, serif;
	--font-mono: JetBrains Mono, monospace;
	--shadow-color: hsl(0 0% 0%);
	--shadow-opacity: 0.1;
	--shadow-blur: 8px;
	--shadow-spread: -1px;
	--shadow-offset-x: 0px;
	--shadow-offset-y: 4px;
	--letter-spacing: 0em;
	--spacing: 0.25rem;
	--shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
	--shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
	--shadow-sm:
		0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 1px 2px -2px hsl(0 0% 0% / 0.1);
	--shadow:
		0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 1px 2px -2px hsl(0 0% 0% / 0.1);
	--shadow-md:
		0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 2px 4px -2px hsl(0 0% 0% / 0.1);
	--shadow-lg:
		0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 4px 6px -2px hsl(0 0% 0% / 0.1);
	--shadow-xl:
		0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 8px 10px -2px hsl(0 0% 0% / 0.1);
	--shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}
	body {
		@apply bg-background text-foreground;
		letter-spacing: var(--tracking-normal);
	}
}
