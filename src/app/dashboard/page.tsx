import {
	dehydrate,
	HydrationBoundary,
	QueryClient,
} from "@tanstack/react-query";
import { headers } from "next/headers";
import { Suspense } from "react";
import StudentLayout from "@/components/layouts/StudentLayout";
import { auth } from "@/lib/auth";
import { getUserWithRoles } from "@/lib/auth/roles";
import { db } from "@/lib/db";
import { appRouter } from "@/server/api/root";
import { createCallerFactory } from "@/server/api/trpc";
import DashboardClient from "./dashboard-client";

// Server component that prefetches data
export default async function DashboardPage() {
	const queryClient = new QueryClient();

	try {
		// Get session from server
		const headersList = await headers();

		const session = await auth.api.getSession({
			headers: headersList as Headers,
		});

		console.log("Dashboard session result:", session);

		if (!session?.user) {
			return (
				<StudentLayout>
					<div className="flex items-center justify-center min-h-96">
						<div className="text-center">
							<h1 className="text-2xl font-bold text-gray-900 mb-4">
								Authentication Required
							</h1>
							<p className="text-gray-600">
								Please sign in to view your dashboard.
							</p>
						</div>
					</div>
				</StudentLayout>
			);
		}

		// Check if user has STUDENT role
		const userWithRoles = await getUserWithRoles(session.user.id);
		const isStudent = userWithRoles?.roles?.some(
			(role) => role.name === "STUDENT",
		);

		if (!isStudent) {
			return (
				<StudentLayout>
					<div className="flex items-center justify-center min-h-96">
						<div className="text-center">
							<h1 className="text-2xl font-bold text-gray-900 mb-4">
								Access Denied
							</h1>
							<p className="text-gray-600">
								This dashboard is only available for students.
							</p>
						</div>
					</div>
				</StudentLayout>
			);
		}

		// Create TRPC caller for server-side data fetching
		const createCaller = createCallerFactory(appRouter);
		const caller = createCaller({
			db,
			user: {
				id: session.user.id,
				name: session.user.name,
				email: session.user.email,
				emailVerified: session.user.emailVerified,
				image: session.user.image,
			},
			userWithRoles,
			req: { headers: headersList } as unknown as Request,
		});

		// Prefetch dashboard data
		await Promise.all([
			queryClient.prefetchQuery({
				queryKey: ["progress", "getUserProgress"],
				queryFn: () => caller.progress.getUserProgress(),
			}),
			queryClient.prefetchQuery({
				queryKey: ["gamification", "getUserStats"],
				queryFn: () => caller.gamification.getUserStats(),
			}),
			queryClient.prefetchQuery({
				queryKey: ["courses", "getMyCourses"],
				queryFn: () => caller.courses.getMyCourses(),
			}),
			queryClient.prefetchQuery({
				queryKey: ["progress", "getRecentActivity"],
				queryFn: () => caller.progress.getRecentActivity(),
			}),
		]);

		return (
			<StudentLayout>
				<HydrationBoundary state={dehydrate(queryClient)}>
					<Suspense fallback={<DashboardLoadingFallback />}>
						<DashboardClient />
					</Suspense>
				</HydrationBoundary>
			</StudentLayout>
		);
	} catch (error) {
		console.error("Error in dashboard page:", error);

		return (
			<StudentLayout>
				<div className="flex items-center justify-center min-h-96">
					<div className="text-center">
						<h1 className="text-2xl font-bold text-gray-900 mb-4">
							Something went wrong
						</h1>
						<p className="text-gray-600">
							Please try refreshing the page or contact support if the problem
							persists.
						</p>
					</div>
				</div>
			</StudentLayout>
		);
	}
}

// Loading fallback component
function DashboardLoadingFallback() {
	return (
		<div className="p-6 space-y-6">
			<div className="mb-8">
				<div className="h-8 bg-gray-200 rounded w-48 mb-2 animate-pulse" />
				<div className="h-4 bg-gray-200 rounded w-64 animate-pulse" />
			</div>

			{/* Stats Overview Skeleton */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				{[1, 2, 3, 4].map((i) => (
					<div key={i} className="border rounded-lg p-6 animate-pulse">
						<div className="flex items-center justify-between">
							<div className="flex-1">
								<div className="h-4 bg-gray-200 rounded w-16 mb-2" />
								<div className="h-8 bg-gray-200 rounded w-12 mb-1" />
								<div className="h-3 bg-gray-200 rounded w-20" />
							</div>
							<div className="w-8 h-8 bg-gray-200 rounded" />
						</div>
					</div>
				))}
			</div>

			{/* Content Grid Skeleton */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{[1, 2].map((i) => (
					<div key={i} className="border rounded-lg p-6 animate-pulse">
						<div className="flex items-center gap-2 mb-4">
							<div className="w-5 h-5 bg-gray-200 rounded" />
							<div className="h-6 bg-gray-200 rounded w-32" />
						</div>
						<div className="space-y-4">
							{[1, 2, 3].map((j) => (
								<div key={j} className="border rounded-lg p-4">
									<div className="h-5 bg-gray-200 rounded w-48 mb-2" />
									<div className="h-2 bg-gray-200 rounded w-full mb-2" />
									<div className="flex justify-between items-center">
										<div className="h-4 bg-gray-200 rounded w-24" />
										<div className="h-8 bg-gray-200 rounded w-20" />
									</div>
								</div>
							))}
						</div>
					</div>
				))}
			</div>

			{/* Recent Achievements Skeleton */}
			<div className="border rounded-lg p-6 animate-pulse">
				<div className="flex items-center gap-2 mb-4">
					<div className="w-5 h-5 bg-gray-200 rounded" />
					<div className="h-6 bg-gray-200 rounded w-40" />
				</div>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					{[1, 2].map((i) => (
						<div
							key={i}
							className="flex items-center gap-4 p-4 border rounded-lg"
						>
							<div className="w-8 h-8 bg-gray-200 rounded" />
							<div className="flex-1">
								<div className="h-5 bg-gray-200 rounded w-32 mb-1" />
								<div className="h-4 bg-gray-200 rounded w-48 mb-1" />
								<div className="h-3 bg-gray-200 rounded w-16" />
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}
