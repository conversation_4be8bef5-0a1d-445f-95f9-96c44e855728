"use client";

import {
	Book<PERSON><PERSON>,
	Calendar,
	CheckCircle,
	Clock,
	PlayCircle,
	TrendingUp,
	Trophy,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useSession } from "@/lib/auth-client";
import { api } from "@/trpc/client";

export default function DashboardClient() {
	const { data: session, isPending } = useSession();

	// Debug: Log client-side session
	console.log("Client-side session:", session);
	console.log("Session pending:", isPending);

	// Fetch real data using TRPC
	const { data: progressData } = api.progress.getUserProgress.useQuery();
	const { data: gamificationData } = api.gamification.getUserStats.useQuery();
	const { data: coursesData } = api.courses.getMyCourses.useQuery();

	// Transform data for display
	const stats = {
		coursesEnrolled: progressData?.coursesEnrolled || 0,
		coursesCompleted: progressData?.coursesCompleted || 0,
		totalXP: gamificationData?.xp?.total || 0,
		currentLevel: gamificationData?.xp?.currentLevel || 1,
		streakDays: gamificationData?.streaks?.daily || 0,
		assignmentsDue: 0, // TODO: Implement assignments
	};

	// Transform courses data for recent courses display
	const recentCourses =
		coursesData?.courses?.slice(0, 3).map((course, index) => ({
			id: course.id,
			title: course.name,
			progress: Math.floor(Math.random() * 100), // TODO: Calculate real progress
			nextLesson: "Continue Learning",
			dueDate: index === 0 ? "Today" : index === 1 ? "Tomorrow" : "In 2 days",
			status: index === 2 ? "completed" : "in-progress",
		})) || [];

	// Mock upcoming assignments (TODO: Replace with real data)
	const upcomingAssignments = [
		{
			id: 1,
			title: "Math Quiz - Quadratic Equations",
			subject: "Mathematics",
			dueDate: "Today, 11:59 PM",
			priority: "high" as const,
		},
		{
			id: 2,
			title: "Physics Lab Report",
			subject: "Physics",
			dueDate: "Tomorrow, 5:00 PM",
			priority: "medium" as const,
		},
		{
			id: 3,
			title: "English Essay - Character Analysis",
			subject: "English",
			dueDate: "In 3 days",
			priority: "low" as const,
		},
	];

	// Transform recent badges for achievements
	const recentAchievements =
		gamificationData?.badges?.recent?.map((badge) => ({
			id: badge.id,
			title: badge.name,
			description: badge.description,
			icon: "🏆", // TODO: Use real icon from badge.iconUrl
			earnedAt: new Date(badge.earnedAt).toLocaleDateString(),
		})) || [];

	return (
		<div className="p-6 space-y-6">
			{/* Welcome Section */}
			<div className="mb-8">
				<h1 className="text-3xl font-bold text-gray-900">
					Welcome back, {session?.user?.name || "Student"}! 👋
				</h1>
				<p className="text-gray-600 mt-2">
					Ready to continue your learning journey? Let's see what's next.
				</p>
			</div>

			{/* Stats Overview */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				<Card>
					<CardContent className="p-6">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-gray-600">Courses</p>
								<p className="text-2xl font-bold text-gray-900">
									{stats.coursesEnrolled}
								</p>
								<p className="text-xs text-gray-500">
									{stats.coursesCompleted} completed
								</p>
							</div>
							<BookOpen className="h-8 w-8 text-blue-600" />
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardContent className="p-6">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-gray-600">Total XP</p>
								<p className="text-2xl font-bold text-gray-900">
									{stats.totalXP}
								</p>
								<p className="text-xs text-gray-500">
									Level {stats.currentLevel}
								</p>
							</div>
							<Trophy className="h-8 w-8 text-yellow-600" />
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardContent className="p-6">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-gray-600">Streak</p>
								<p className="text-2xl font-bold text-gray-900">
									{stats.streakDays}
								</p>
								<p className="text-xs text-gray-500">days</p>
							</div>
							<TrendingUp className="h-8 w-8 text-green-600" />
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardContent className="p-6">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-gray-600">Due Soon</p>
								<p className="text-2xl font-bold text-gray-900">
									{stats.assignmentsDue}
								</p>
								<p className="text-xs text-gray-500">assignments</p>
							</div>
							<Clock className="h-8 w-8 text-red-600" />
						</div>
					</CardContent>
				</Card>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Recent Courses */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<BookOpen className="h-5 w-5" />
							Continue Learning
						</CardTitle>
						<CardDescription>Pick up where you left off</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						{recentCourses.length > 0 ? (
							recentCourses.map((course) => (
								<div key={course.id} className="border rounded-lg p-4">
									<div className="flex items-center justify-between mb-2">
										<h4 className="font-medium text-gray-900">
											{course.title}
										</h4>
										{course.status === "completed" ? (
											<CheckCircle className="h-5 w-5 text-green-600" />
										) : (
											<PlayCircle className="h-5 w-5 text-blue-600" />
										)}
									</div>
									<div className="space-y-2">
										<div className="flex justify-between text-sm text-gray-600">
											<span>Progress</span>
											<span>{course.progress}%</span>
										</div>
										<Progress value={course.progress} className="h-2" />
										<div className="flex justify-between items-center">
											<span className="text-sm text-gray-600">
												{course.nextLesson}
											</span>
											<Button size="sm" variant="outline">
												Continue
											</Button>
										</div>
									</div>
								</div>
							))
						) : (
							<div className="text-center py-8">
								<BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
								<p className="text-gray-600">No courses enrolled yet</p>
								<Button className="mt-4" size="sm">
									Browse Courses
								</Button>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Upcoming Assignments */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Calendar className="h-5 w-5" />
							Upcoming Assignments
						</CardTitle>
						<CardDescription>Don't miss these deadlines</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						{upcomingAssignments.map((assignment) => (
							<div key={assignment.id} className="border rounded-lg p-4">
								<div className="flex items-start justify-between">
									<div className="flex-1">
										<h4 className="font-medium text-gray-900">
											{assignment.title}
										</h4>
										<p className="text-sm text-gray-600">
											{assignment.subject}
										</p>
										<div className="flex items-center gap-2 mt-2">
											<Clock className="h-4 w-4 text-gray-400" />
											<span className="text-sm text-gray-600">
												{assignment.dueDate}
											</span>
											<Badge
												variant={
													assignment.priority === "high"
														? "destructive"
														: assignment.priority === "medium"
															? "default"
															: "secondary"
												}
											>
												{assignment.priority}
											</Badge>
										</div>
									</div>
								</div>
							</div>
						))}
					</CardContent>
				</Card>
			</div>

			{/* Recent Achievements */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Trophy className="h-5 w-5" />
						Recent Achievements
					</CardTitle>
					<CardDescription>Celebrate your progress!</CardDescription>
				</CardHeader>
				<CardContent>
					{recentAchievements.length > 0 ? (
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{recentAchievements.map((achievement) => (
								<div
									key={achievement.id}
									className="flex items-center gap-4 p-4 border rounded-lg"
								>
									<div className="text-2xl">{achievement.icon}</div>
									<div className="flex-1">
										<h4 className="font-medium text-gray-900">
											{achievement.title}
										</h4>
										<p className="text-sm text-gray-600">
											{achievement.description}
										</p>
										<p className="text-xs text-gray-500 mt-1">
											{achievement.earnedAt}
										</p>
									</div>
								</div>
							))}
						</div>
					) : (
						<div className="text-center py-8">
							<Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
							<p className="text-gray-600">No achievements yet</p>
							<p className="text-sm text-gray-500">
								Complete activities to earn your first badge!
							</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
