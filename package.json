{"name": "code", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "format": "biome format ./src --write", "check": "biome check --write ./src", "check!": "biome check --write --unsafe --no-errors-on-unmatched ./src", "clean": "rm -rf .next .output node_modules && pnpm install && pnpm dev", "build:check": "pnpm check! && pnpm build", "db:generate": "pnpm drizzle-kit generate", "db:migrate": "pnpm drizzle-kit migrate", "db:push": "pnpm drizzle-kit push", "db:drop": "pnpm drizzle-kit drop", "db:introspect": "pnpm drizzle-kit introspect", "db:check": "pnpm drizzle-kit check", "db:studio": "pnpm drizzle-kit studio", "db:seed": "tsx scripts/seed.ts", "db:reset": "tsx scripts/reset-db.ts", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@trpc/client": "^11.4.3", "@trpc/next": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "bcryptjs": "^3.0.2", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^17.0.1", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "motion": "^12.22.0", "next": "15.3.4", "next-themes": "^0.4.6", "postgres": "^3.4.7", "prettier": "^3.6.2", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "react-resizable-panels": "^3.0.3", "recharts": "^3.0.2", "resend": "^4.6.0", "sonner": "^2.0.5", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^3.0.0", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "drizzle-seed": "^0.3.1", "husky": "^9.1.7", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}