# LearnFunda

A modern learning platform built with cutting-edge web technologies.

## 🚀 Tech Stack

- **Framework**: [Next.js 15](https://nextjs.org) with App Router
- **Language**: [TypeScript](https://www.typescriptlang.org/) with strict mode
- **Styling**: [Tailwind CSS 4](https://tailwindcss.com/) with [Shadcn/ui](https://ui.shadcn.com/)
- **Code Quality**: [Biome.js](https://biomejs.dev/) for linting and formatting
- **Environment**: [T3 Env](https://env.t3.gg/) for type-safe environment variables
- **Package Manager**: [PNPM](https://pnpm.io/)
- **Git Hooks**: [<PERSON><PERSON>](https://typicode.github.io/husky/) for pre-commit checks
- **Fonts**: [Geist](https://vercel.com/font) font family

## 📋 Prerequisites

- **Node.js**: Version 18.17 or later
- **PNPM**: Latest version (recommended package manager)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd code
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your environment variables
   ```

## 🚀 Development

### Start the development server

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Available Scripts

- `pnpm dev` - Start development server with Turbopack
- `pnpm build` - Build the application for production
- `pnpm start` - Start the production server
- `pnpm format` - Format code with Biome
- `pnpm check` - Run linting and formatting checks
- `pnpm check!` - Run checks with auto-fix (unsafe mode)
- `pnpm build:check` - Run checks and build (used in CI/CD)
- `pnpm clean` - Clean build artifacts and reinstall dependencies

## 🔧 Code Quality

This project enforces code quality through:

### Biome.js
- **Linting**: ESLint-compatible rules with performance optimizations
- **Formatting**: Prettier-compatible formatting with faster execution
- **Import Organization**: Automatic import sorting and organization

### Husky Git Hooks
- **Pre-commit**: Runs `pnpm build:check` before each commit
- **Bypass**: Use `git commit --no-verify` to skip checks (not recommended)

### TypeScript
- **Strict Mode**: Enabled for maximum type safety
- **Path Mapping**: `@/*` aliases for clean imports
- **Next.js Integration**: Optimized for Next.js App Router

## 🌍 Environment Variables

Environment variables are managed with T3 Env for type safety:

```typescript
// src/env.ts
export const env = createEnv({
  server: {
    RESEND_API_KEY: z.string().min(1),
  },
  client: {
    // Add client-side env vars here
  },
  runtimeEnv: {
    RESEND_API_KEY: process.env.RESEND_API_KEY,
  },
});
```

## 📁 Project Structure

```
src/
├── app/                 # Next.js App Router pages
│   ├── api/            # API routes
│   ├── globals.css     # Global styles with Tailwind
│   ├── layout.tsx      # Root layout
│   └── page.tsx        # Home page
├── components/         # Reusable components
│   └── ui/            # Shadcn/ui components
├── lib/               # Utility functions
└── env.ts             # Environment configuration
```

## 🎨 Styling

### Tailwind CSS 4
- **CSS-first**: Uses `@import "tailwindcss"` in globals.css
- **Custom Theme**: Configured with design tokens
- **Dark Mode**: Built-in dark mode support

### Shadcn/ui
- **Components**: Pre-built, accessible components
- **Customization**: Easily customizable with CSS variables
- **Icons**: Lucide React icons included

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your repository to [Vercel](https://vercel.com)
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Other Platforms
The application can be deployed to any platform that supports Node.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. **Code Style**: Follow the established Biome.js configuration
2. **Commits**: Ensure pre-commit hooks pass
3. **Testing**: Add tests for new features
4. **Documentation**: Update README for significant changes

## 📚 Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Shadcn/ui Documentation](https://ui.shadcn.com)
- [Biome.js Documentation](https://biomejs.dev)
- [T3 Env Documentation](https://env.t3.gg)
